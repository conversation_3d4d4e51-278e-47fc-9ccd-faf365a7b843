// ./components/zshell/editor/hooks/useEditorChange.ts
import { useCallback } from 'react';

/**
 * Hook 属性接口
 */
interface UseEditorChangeProps {
  updateTabContent: (tabId: string, content: string) => void;
}

/**
 * Hook: 管理编辑器内容变化逻辑
 * 
 * 负责处理编辑器内容变化时的回调逻辑
 */
export function useEditorChange({
  updateTabContent
}: UseEditorChangeProps) {
  
  /**
   * Monaco Editor 内容变化时的回调函数
   */
  const handleEditorChange = useCallback((
    value: string | undefined,
    changedTabId: string // 传入变化的 Tab ID
  ) => {
    // 更新对应标签页的内容状态
    updateTabContent(changedTabId, value || "");
    // 内容变化后，高亮状态可能需要更新 (由高亮 Hook 的 effect 处理)
  }, [updateTabContent]); // 依赖更新内容的函数

  return { handleEditorChange };
}
