"use client"

import React from "react";
import { Loader } from "@/components/ui/loader";

interface LoadingFallbackProps {
  /**
   * 加载文本，默认为"加载中..."
   */
  text?: string;
  /**
   * 加载器大小，默认为"md"
   */
  size?: "sm" | "md" | "lg" | "xl";
  /**
   * 加载器变体，默认为"primary"
   */
  variant?: "default" | "primary" | "secondary" | "muted";
  /**
   * 自定义类名
   */
  className?: string;
}

/**
 * 通用加载中回退组件，用于 React.Suspense 和懒加载
 */
export function LoadingFallback({
  text = "加载中...",
  size = "md",
  variant = "primary",
  className,
}: LoadingFallbackProps) {
  return (
    <div className={`flex items-center justify-center h-full w-full ${className || ""}`}>
      <div className="flex items-center gap-2">
        <Loader variant={variant} size={size} />
        <span className={`text-${variant}`}>{text}</span>
      </div>
    </div>
  );
}

export default LoadingFallback;
