"use client"

import React, { useState, useEffect } from 'react';

// 路由组件
import { Link, useNavigate, useLocation } from 'react-router-dom';

// lucide图标
import { SquareTerminal, Plus, X } from "lucide-react"

// shadcn/ui组件
import {
  SidebarGroup,
  SidebarGroupLabel,
  SidebarMenu,
  SidebarMenuButton,
  SidebarMenuItem,
  SidebarMenuAction,
} from "@/components/ui/sidebar"
import { Button } from '@/components/ui/button';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"

// hooks
import { useToast } from "@/hooks/use-toast"

// Tauri API
import { disconnectTcpServer } from "@/components/utils/env-utils"

// 单板连接类型
interface BoardConnection {
  id: string;
  boardId: string;
  title: string;
  path: string;
}

// 单板导航组件
export function NavBoards({
  conn_types, activeItem, setActiveItem, connectionId
}: {
  conn_types: {
    title: string
    path: string
  }[],
  activeItem: string,
  setActiveItem: (item: string) => void,
  connectionId: string | null,
}) {
  const [boardConnections, setBoardConnections] = useState<BoardConnection[]>([]);
  const [showNewBoardDialog, setShowNewBoardDialog] = useState(false);
  const [newBoardId, setNewBoardId] = useState('');
  const { toast } = useToast();
  const navigate = useNavigate();
  const location = useLocation();

  // 当连接断开时，清空所有单板连接
  useEffect(() => {
    if (!connectionId) {
      setBoardConnections([]);
    }
  }, [connectionId]);

  // 添加新的单板连接
  const handleAddBoardConnection = () => {
    if (!connectionId) {
      toast({
        title: "无法打开串口",
        description: "请先连接到一个环境",
        variant: "destructive",
      });
      return;
    }

    if (!newBoardId.trim()) {
      toast({
        title: "单板ID不能为空",
        description: "请输入有效的单板ID",
        variant: "destructive",
      });
      return;
    }

    // 检查是否已存在相同的单板ID
    if (boardConnections.some(conn => conn.boardId === newBoardId.trim())) {
      toast({
        title: "单板ID已存在",
        description: "该单板ID已经存在，请使用不同的ID",
        variant: "destructive",
      });
      return;
    }

    const newConnection: BoardConnection = {
      id: `${connectionId}-${newBoardId.trim()}`,
      boardId: newBoardId.trim(),
      title: `单板 ${newBoardId.trim()}`,
      path: `/terminal/${connectionId}/${newBoardId.trim()}`,
    };

    setBoardConnections(prev => [...prev, newConnection]);
    setNewBoardId('');
    setShowNewBoardDialog(false);

    // 自动跳转到新创建的单板页面
    navigate(newConnection.path);
    setActiveItem(newConnection.title);

    toast({
      title: "串口已打开",
      description: `已为单板 ${newBoardId.trim()} 打开新的串口`,
      duration: 2000,
    });
  };

  // 移除单板连接
  const handleRemoveBoardConnection = async (connectionToRemove: BoardConnection) => {
    try {
      // 检查当前是否在要关闭的串口页面上
      const isCurrentPage = location.pathname === connectionToRemove.path;

      // 如果当前在要关闭的串口页面上，先导航到主页
      if (isCurrentPage) {
        navigate('/');
        setActiveItem('');
      }

      // 从UI列表中移除串口
      setBoardConnections(prev => prev.filter(conn => conn.id !== connectionToRemove.id));

      // 如果有连接ID，尝试断开TCP连接
      // 注意：这里我们不需要断开主连接，只是清理UI状态
      // 因为单板连接共享同一个TCP连接，只是通过boardId区分

      toast({
        title: "串口已关闭",
        description: `已关闭单板 ${connectionToRemove.boardId} 的串口`,
        duration: 2000,
      });
    } catch (error) {
      console.error('关闭串口时发生错误:', error);
      toast({
        title: "关闭串口失败",
        description: `关闭单板 ${connectionToRemove.boardId} 的串口时发生错误`,
        variant: "destructive",
      });
    }
  };

  return (
    <SidebarGroup>
      <SidebarGroupLabel>主控/单板串口</SidebarGroupLabel>
      <SidebarMenu>
        {/* 原有的连接类型 */}
        {conn_types.map((item) => (
          <SidebarMenuItem key={item.title} onClick={() => setActiveItem(item.title)}>
            <Link to={item.path} className="flex items-center w-full h-full">
              <SidebarMenuButton tooltip={item.title} isActive={activeItem === item.title}>
                <SquareTerminal />
                <span>{item.title}</span>
              </SidebarMenuButton>
              <SidebarMenuAction showOnHover>
                <X />
              </SidebarMenuAction>
            </Link>
          </SidebarMenuItem>
        ))}

        {/* 动态添加的单板连接 */}
        {boardConnections.map((connection) => (
          <SidebarMenuItem key={connection.id} onClick={() => setActiveItem(connection.title)}>
            <Link to={connection.path} className="flex items-center w-full h-full">
              <SidebarMenuButton tooltip={connection.title} isActive={activeItem === connection.title}>
                <SquareTerminal />
                <span>{connection.title}</span>
              </SidebarMenuButton>
              <SidebarMenuAction
                showOnHover
                onClick={(e) => {
                  e.preventDefault();
                  e.stopPropagation();
                  handleRemoveBoardConnection(connection);
                }}
              >
                <X />
              </SidebarMenuAction>
            </Link>
          </SidebarMenuItem>
        ))}

        {/* 打开新串口按钮 */}
        <SidebarMenuItem>
          <Dialog open={showNewBoardDialog} onOpenChange={setShowNewBoardDialog}>
            <DialogTrigger asChild>
              <SidebarMenuButton
                className="text-sidebar-foreground/70"
                disabled={!connectionId}
              >
                <Plus className="text-sidebar-foreground/70" />
                <span>打开新的串口...</span>
              </SidebarMenuButton>
            </DialogTrigger>
            <DialogContent className="sm:max-w-[425px]">
              <DialogHeader>
                <DialogTitle>打开新的串口</DialogTitle>
                <DialogDescription>
                  请输入要连接的单板ID
                </DialogDescription>
              </DialogHeader>
              <div className="grid gap-4 py-4">
                <div className="grid grid-cols-4 items-center gap-4">
                  <Label htmlFor="boardId" className="text-right">
                    单板ID
                  </Label>
                  <Input
                    id="boardId"
                    value={newBoardId}
                    onChange={(e) => setNewBoardId(e.target.value)}
                    className="col-span-3"
                    placeholder="例如: 001, A1, etc."
                    onKeyDown={(e) => {
                      if (e.key === 'Enter') {
                        handleAddBoardConnection();
                      }
                    }}
                  />
                </div>
              </div>
              <DialogFooter>
                <Button
                  type="button"
                  variant="outline"
                  onClick={() => setShowNewBoardDialog(false)}
                >
                  取消
                </Button>
                <Button type="submit" onClick={handleAddBoardConnection}>
                  打开串口
                </Button>
              </DialogFooter>
            </DialogContent>
          </Dialog>
        </SidebarMenuItem>
      </SidebarMenu>
    </SidebarGroup>
  )
}
