/* 编辑器标签页相关样式 */

/* 标签栏滚动容器 */
.tabListScroll {
  scrollbar-width: none; /* Firefox 隐藏滚动条 */
}

.tabListScroll::-webkit-scrollbar {
  display: none; /* Webkit 浏览器隐藏滚动条 */
}

/* 固定在右侧的按钮组 */
.fixedTabButtons {
  position: absolute;
  top: 0;
  right: 0;
  height: 100%;
  border-left: 1px solid var(--border);
  /* 确保完全不透明的背景 */
  background-color: var(--background);
  /* 添加渐变效果和阴影，增强视觉分隔 */
  box-shadow: -4px 0 8px -2px rgba(0, 0, 0, 0.05);
  /* 确保完全不透明 */
  -webkit-backdrop-filter: none;
  backdrop-filter: none;
  /* 增加z-index确保在标签页上层 */
  z-index: 10;
}

/* 编辑器容器 */
.editorContainer {
  position: absolute;
  inset: 0;
  width: 100%;
  height: 100%;
}

/* 可见的编辑器 */
.editorVisible {
  visibility: visible;
}

/* 隐藏的编辑器 */
.editorHidden {
  visibility: hidden;
}
