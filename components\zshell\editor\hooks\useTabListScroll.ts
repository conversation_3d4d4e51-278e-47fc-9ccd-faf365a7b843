// ./components/zshell/editor/hooks/useTabListScroll.ts
import { useRef, useEffect, useCallback } from 'react';

/**
 * Hook: 为元素启用鼠标滚轮水平滚动
 * @param scrollSensitivity 滚动敏感度，数值越大滚动越快
 * @returns 返回一个 ref，需要附加到要滚动的容器元素上
 */
export function useTabListScroll(scrollSensitivity: number = 1) {
  const scrollContainerRef = useRef<HTMLDivElement>(null);

  const handleWheelScroll = useCallback((event: WheelEvent) => {
    const container = scrollContainerRef.current;
    if (!container) return;

    // event.deltaY > 0 表示向下滚动, event.deltaY < 0 表示向上滚动
    if (event.deltaY !== 0) {
      // 阻止页面的垂直滚动
      event.preventDefault();
      // 根据滚轮方向调整 scrollLeft
      container.scrollLeft += event.deltaY * scrollSensitivity;
    }
    // 如果需要支持水平滚轮 (例如某些鼠标)，可以添加对 event.deltaX 的处理
    // if (event.deltaX !== 0) {
    //   event.preventDefault();
    //   container.scrollLeft += event.deltaX * scrollSensitivity;
    // }

  }, [scrollSensitivity]);

  useEffect(() => {
    const container = scrollContainerRef.current;
    if (container) {
      // 添加事件监听器
      // passive: false 是必要的，因为我们调用了 event.preventDefault()
      container.addEventListener('wheel', handleWheelScroll, { passive: false });
    }

    // 清理函数：移除事件监听器
    return () => {
      if (container) {
        container.removeEventListener('wheel', handleWheelScroll);
      }
    };
  }, [handleWheelScroll]); // 依赖 handleWheelScroll 回调

  // 返回 ref，以便父组件可以将其附加到目标元素
  return scrollContainerRef;
}