// ./components/zshell/editor/hooks/useEditorHighlighting.ts
import { useState, useRef, useEffect, useCallback } from 'react';
import * as monaco from 'monaco-editor';
import type { editor } from 'monaco-editor';
import { HIGHLIGHT_COLORS, MAX_HIGHLIGHTS, HIGHLIGHT_CLASS_PREFIX } from '../editor-constants';

interface UseEditorHighlightingProps {
	editorsRef: React.MutableRefObject<Record<string, editor.IStandaloneCodeEditor>>;
	activeTabId: string | null;
}

/**
 * Hook: 管理 Monaco 编辑器中的单词高亮功能
 *
 * 负责添加/移除高亮词、应用/清除装饰器、管理高亮颜色和样式。
 */
export function useEditorHighlighting({ editorsRef, activeTabId }: UseEditorHighlightingProps) {
	const styleRef = useRef<HTMLStyleElement | null>(null); // 动态样式表引用
	const decorationsRef = useRef<Record<string, editor.IEditorDecorationsCollection>>({}); // 装饰器引用
	const [highlightWords, setHighlightWords] = useState<string[]>([]); // 当前高亮的单词列表
	const [highlightWordColors, setHighlightWordColors] = useState<Map<string, number>>(new Map()); // 单词 -> 颜色索引映射

	/**
	 * 获取一个随机的颜色索引
	 */
	const getRandomColorIndex = useCallback(() => {
		// 确保颜色索引不重复，直到所有颜色都用过一遍 (如果需要)
		// 这里简单随机选择
		return Math.floor(Math.random() * HIGHLIGHT_COLORS.length);
	}, []);

	/**
		* 切换当前光标下单词的高亮状态
		*/
	const toggleHighlightAtCursor = useCallback(() => {
		if (!activeTabId || !editorsRef.current[activeTabId]) return;

		const editor = editorsRef.current[activeTabId];
		const position = editor.getPosition();
		const model = editor.getModel();
		if (!position || !model) return;

		const wordInfo = model.getWordAtPosition(position);
		const word = wordInfo?.word.trim();
		if (!word) return;

		// 使用函数式更新来访问最新的状态
		setHighlightWords(prevWords => {
			const isWordHighlighted = prevWords.includes(word);

			// 更新颜色映射
			setHighlightWordColors(prevColors => {
				const newColors = new Map(prevColors);

				if (isWordHighlighted) {
					newColors.delete(word);
				} else {
					newColors.set(word, getRandomColorIndex());
				}

				return newColors;
			});

			// 更新高亮词列表
			return isWordHighlighted
				? prevWords.filter(w => w !== word)
				: [...prevWords, word];
		});
	}, [activeTabId, editorsRef, getRandomColorIndex]); // 移除 highlightWords 和 highlightWordColors 依赖

	/**
	 * 应用高亮装饰器到所有打开的编辑器实例
	 */
	const applyHighlightsToAllEditors = useCallback(() => {
		Object.entries(editorsRef.current).forEach(([tabId, editorInstance]) => {
			const model = editorInstance.getModel();
			if (!model) return;

			// 清理旧的装饰器
			if (decorationsRef.current[tabId]) {
				decorationsRef.current[tabId].clear();
			}

			// 如果没有需要高亮的词，直接返回
			if (highlightWords.length === 0) {
				delete decorationsRef.current[tabId]; // 清理引用
				return;
			}

			// 为每个高亮词查找匹配项并创建装饰器
			const allDecorations = highlightWords.flatMap((word) => {
				const colorIndex = highlightWordColors.get(word);
				if (colorIndex === undefined) return []; // 如果没有颜色映射，跳过
				const matches = model.findMatches(word, true, false, true, null, true); // 查找所有匹配
				const limitedMatches = matches.slice(0, MAX_HIGHLIGHTS); // 限制数量

				return limitedMatches.map(match => ({
					range: match.range, // 直接使用 Monaco 的 Range 对象
					options: {
						inlineClassName: `${HIGHLIGHT_CLASS_PREFIX}-${colorIndex}`, // 应用 CSS 类
						stickiness: monaco.editor.TrackedRangeStickiness.NeverGrowsWhenTypingAtEdges, // 文本编辑时装饰器的行为
					}
				}));
			});

			// 应用新的装饰器集合
			decorationsRef.current[tabId] = editorInstance.createDecorationsCollection(allDecorations);
		});
	}, [editorsRef, highlightWords, highlightWordColors]); // 依赖编辑器引用和高亮状态

	/**
	 * 清除所有编辑器中的所有高亮
	 */
	const clearAllHighlights = useCallback(() => {
		Object.values(editorsRef.current).forEach(editorInstance => {
			const model = editorInstance.getModel();
			if (!model) return;
			// 清除该编辑器的所有高亮装饰器
			const decorations = model.getAllDecorations().filter(d =>
				d.options.inlineClassName?.startsWith(HIGHLIGHT_CLASS_PREFIX)
			);
			model.deltaDecorations(decorations.map(d => d.id), []);
		});

		// 清理内部状态
		setHighlightWords([]);
		setHighlightWordColors(new Map());
		decorationsRef.current = {}; // 清空装饰器引用
	}, [editorsRef]); // 依赖编辑器引用

	// --- Effects ---

	// Effect 1: 注入/移除高亮 CSS 样式
	useEffect(() => {
		if (styleRef.current) return; // 防止重复注入

		const styleElement = document.createElement('style');
		styleElement.id = "editor-highlight-styles"; // 给 ID 方便调试
		styleElement.textContent = HIGHLIGHT_COLORS.map((color, index) => `
      .${HIGHLIGHT_CLASS_PREFIX}-${index} {
        background-color: ${color} !important; /* 使用 !important 提高优先级 */
        border-radius: 3px;
        color: white !important; /* 确保文字颜色可见 */
        box-shadow: 0 0 0 1px ${color}; /* 轻微边框 */
      }
    `).join('\n');

		document.head.appendChild(styleElement);
		styleRef.current = styleElement;

		// 组件卸载时移除样式
		return () => {
			styleRef.current?.remove();
			styleRef.current = null;
		};
	}, []); // 空依赖，只运行一次

	// Effect 2: 当高亮词列表或颜色映射变化时，重新应用高亮
	useEffect(() => {
		applyHighlightsToAllEditors();
	}, [highlightWords, highlightWordColors, applyHighlightsToAllEditors]); // 依赖高亮状态和应用函数

	// Effect 3: 当活动标签页切换时，也可能需要重新应用高亮（确保新激活的编辑器有高亮）
	// 或者在 handleEditorMount 中首次应用高亮
	useEffect(() => {
		if (activeTabId && editorsRef.current[activeTabId]) {
			// 确保当前活动编辑器的装饰器是最新的
			applyHighlightsToAllEditors(); // 重新应用到所有编辑器，保证一致性
		}
	}, [activeTabId, editorsRef, applyHighlightsToAllEditors]);

	return {
		toggleHighlightAtCursor,
		clearAllHighlights,
		applyHighlightsToEditor: applyHighlightsToAllEditors, // 暴露给外部，例如 editor mount 时调用
	};
}
