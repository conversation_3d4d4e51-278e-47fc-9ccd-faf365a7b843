"use client";

import { TabsTrigger } from "@/components/ui/tabs";
import { Text, Circle, X } from "lucide-react";

// TabTrigger组件需要的标签页信息子集
export interface TabTriggerInfo {
  id: string;
  title: string;
  isSaved: boolean; // 保持 isSaved
}

// TabTrigger组件的属性接口
export interface TabTriggerProps {
  tab: TabTriggerInfo; // 使用新的接口名
  active: boolean;
  onSelect: (id: string) => void;
  onClose: (id: string) => void;
}

/**
 * TabTrigger组件 - 编辑器标签页触发器
 *
 * 显示单个标签页，包含文件名、保存状态指示器和关闭按钮
 * 支持点击选择和关闭标签页
 */
export const TabTrigger = ({
  tab,
  active,
  onSelect,
  onClose
}: TabTriggerProps) => {
  return (
    <TabsTrigger
      value={tab.id}
      className={`group h-9 pt-1 pl-3 pr-2 relative rounded-none flex items-center flex-shrink-0
        data-[state=active]:text-foreground
        data-[state=active]:bg-background
        hover:bg-muted
        text-muted-foreground hover:text-foreground
        transition-none
        border-r border-border
        max-w-[200px] // 可选：限制标签页最大宽度
      `}
      onClick={() => onSelect(tab.id)}
      title={tab.title} // 添加 title 属性显示完整文件名
    >
      {/* 活动标签页顶部指示条 */}
      {active && (
        <div className="absolute top-0 left-0 right-0 h-[2px] bg-primary" />
      )}

      <div className="flex items-center gap-1.5 overflow-hidden"> {/* 添加 overflow-hidden */}
        {/* 文件图标 */}
        <Text className="h-3.5 w-3.5 text-muted-foreground flex-shrink-0" strokeWidth={1.5} />

        {/* 文件名 - 使用 truncate 省略长文件名 */}
        <span className="text-xs truncate">
          {tab.title}
        </span>

        {/* 未保存指示器 */}
        {!tab.isSaved && (
          <Circle className="ml-1 h-1.5 w-1.5 fill-current flex-shrink-0" />
        )}
      </div>

      {/* 关闭按钮 - 仅在悬停时显示 */}
      <X
        className={`ml-2 h-4 w-4 p-[2px] hover:bg-muted rounded-sm opacity-0 group-hover:opacity-100 flex-shrink-0`}
        onClick={(e) => {
          e.stopPropagation(); // 阻止触发 onSelect
          onClose(tab.id);
        }}
      />
    </TabsTrigger>
  );
};

export default TabTrigger;