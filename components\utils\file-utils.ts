// ./utils/file-utils.ts
import { invoke } from '@tauri-apps/api/core';
import { readTextFile, writeTextFile } from '@tauri-apps/plugin-fs';

/**
 * 检查文件是否存在 (调用 Tauri 后端)
 * @param path 文件路径
 * @returns Promise<boolean> 文件是否存在
 */
export const checkFileExists = async (path: string): Promise<boolean> => {
  try {
    return await invoke<boolean>('exists_file', { path });
  } catch (error) {
    console.error(`检查文件存在性失败 (${path}):`, error);
    return false; // 出错时默认文件不存在
  }
};

/**
 * 读取文本文件内容
 * @param filePath 文件路径
 * @returns Promise<string> 文件内容
 * @throws 如果读取失败会抛出错误
 */
export const readFileContent = async (filePath: string): Promise<string> => {
  return readTextFile(filePath);
};

/**
 * 写入文本文件内容
 * @param filePath 文件路径
 * @param content 文件内容
 * @returns Promise<void>
 * @throws 如果写入失败会抛出错误
 */
export const writeFileContent = async (filePath: string, content: string): Promise<void> => {
  try {
    // 确保文件路径有效
    if (!filePath || filePath.trim() === '') {
      throw new Error('无效的文件路径');
    }

    // 尝试写入文件
    await writeTextFile(filePath, content);

    // 验证文件是否写入成功
    const exists = await checkFileExists(filePath);
    if (!exists) {
      throw new Error(`文件写入后不存在: ${filePath}`);
    }
  } catch (error) {
    console.error(`写入文件失败:`, error);
    throw error; // 重新抛出错误以便上层处理
  }
};