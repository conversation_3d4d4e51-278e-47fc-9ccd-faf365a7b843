"use client"

import React, { useState, useEffect } from 'react';

interface ErrorTestProps {
  /**
   * 是否立即抛出错误
   */
  throwImmediately?: boolean;
  /**
   * 延迟多少毫秒后抛出错误
   */
  delayMs?: number;
  /**
   * 自定义错误消息
   */
  errorMessage?: string;
}

/**
 * 测试组件 - 用于测试 ErrorBoundary
 * 这个组件会故意抛出错误，以触发 ErrorBoundary
 */
export function ErrorTest({
  throwImmediately = false,
  delayMs = 3000,
  errorMessage = "测试一下出错界面 (*￣︶￣*)"
}: ErrorTestProps) {
  const [shouldThrow, setShouldThrow] = useState(throwImmediately);
  const [countdown, setCountdown] = useState(delayMs / 1000);

  // 如果不是立即抛出，则设置定时器
  useEffect(() => {
    if (!throwImmediately) {
      const timer = setInterval(() => {
        setCountdown(prev => {
          if (prev <= 1) {
            clearInterval(timer);
            setShouldThrow(true);
            return 0;
          }
          return prev - 1;
        });
      }, 1000);

      return () => clearInterval(timer);
    }
  }, [throwImmediately, delayMs]);

  // 手动触发错误的函数
  const triggerError = () => {
    setShouldThrow(true);
  };

  // 如果 shouldThrow 为 true，则抛出错误
  if (shouldThrow) {
    throw new Error(errorMessage);
  }

  // 正常渲染
  return (
    <div className="p-6 border rounded-lg bg-card">
      <h2 className="text-xl font-bold mb-4">ErrorBoundary 测试组件</h2>
      
      {!throwImmediately && (
        <div className="mb-4">
          <p className="text-muted-foreground mb-2">
            将在 <span className="font-bold text-primary">{countdown}</span> 秒后抛出错误
          </p>
          <div className="w-full bg-secondary rounded-full h-2">
            <div 
              className="bg-primary h-2 rounded-full transition-all duration-1000" 
              style={{ width: `${(countdown / (delayMs / 1000)) * 100}%` }}
            ></div>
          </div>
        </div>
      )}
      
      <div className="flex gap-2">
        <button
          type="button"
          className="px-4 py-2 bg-primary text-primary-foreground rounded-md hover:bg-primary/90"
          onClick={triggerError}
        >
          立即触发错误
        </button>
      </div>
    </div>
  );
}

export default ErrorTest;
