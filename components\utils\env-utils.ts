// ./utils/env-utils.ts
import { invoke } from '@tauri-apps/api/core';
import { appConfigDir } from '@tauri-apps/api/path';

// 环境状态文件路径
export const ENV_STATE_FILE = '.env-state.json';

/**
 * 获取环境状态文件的完整路径
 * @returns Promise<string> 状态文件的路径
 */
export const getEnvStatePath = async (): Promise<string> => {
  const configDir = await appConfigDir();
  return `${configDir}${ENV_STATE_FILE}`;
};

/**
 * Ping TCP服务器
 * @param addr 服务器地址
 * @param port 服务器端口
 * @returns Promise<boolean> 是否可以连接
 */
export const pingTcpServer = async (addr: string, port: number): Promise<boolean> => {
  try {
    return await invoke<boolean>('ping_tcp_server', { addr, port });
  } catch (error) {
    console.error('Ping TCP服务器失败:', error);
    return false;
  }
};

/**
 * 连接TCP服务器
 * @param addr 服务器地址
 * @param port 服务器端口
 * @returns Promise<string> 连接ID
 */
export const connectTcpServer = async (addr: string, port: number): Promise<string> => {
  return await invoke<string>('connect_tcp_server', { addr, port });
};

/**
 * 断开TCP服务器连接
 * @param id 连接ID
 * @returns Promise<void>
 */
export const disconnectTcpServer = async (id: string): Promise<void> => {
  await invoke<void>('disconnect_tcp_server', { id });
};

/**
 * 发送TCP数据
 * @param id 连接ID
 * @param data 数据（十六进制字符串）
 * @returns Promise<void>
 */
export const sendTcpData = async (id: string, data: string): Promise<void> => {
  await invoke<void>('send_tcp_data', { id, data });
};

/**
 * 发送TCP数据给特定单板
 * @param id 连接ID
 * @param data 数据（十六进制字符串）
 * @param boardId 单板ID
 * @returns Promise<void>
 */
export const sendTcpDataBoard = async (id: string, data: string, boardId: string): Promise<void> => {
  await invoke<void>('send_tcp_data_board', { id, data, boardId });
};