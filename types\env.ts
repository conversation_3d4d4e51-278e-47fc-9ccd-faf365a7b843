// types/env.ts

/**
 * 环境信息接口定义
 */
export interface Environment {
  id: string;          // 唯一标识符
  name: string;        // 环境名称
  addr: string;        // IP地址
  port: number;        // 端口号
  username?: string;   // 用户名 (可选)
  password?: string;   // 密码 (可选)
  createdAt: string;   // 创建时间
  updatedAt: string;   // 更新时间
}

/**
 * 用于持久化的环境状态接口
 */
export interface EnvironmentState {
  environments: Environment[];
  activeEnvId: string | null; // 当前活动环境的 ID
}

/**
 * 环境连接状态
 */
export interface ConnectionStatus {
  envId: string;
  isConnected: boolean;
  connectionId?: string; // Tauri 后端返回的连接ID
  lastConnectedAt?: string;
}

/**
 * Ping 测试结果
 */
export interface PingResult {
  success: boolean;
  responseTime?: number; // 响应时间(毫秒)
  error?: string;        // 错误信息
}

/**
 * 新增环境表单数据
 */
export interface NewEnvironmentForm {
  name: string;
  addr: string;
  port: string;
  username: string;
  password: string;
}

/**
 * 环境表单验证错误
 */
export interface EnvironmentFormErrors {
  name?: string;
  addr?: string;
  port?: string;
  username?: string;
  password?: string;
}
