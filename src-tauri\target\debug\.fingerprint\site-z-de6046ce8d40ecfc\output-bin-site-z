{"$message_type":"diagnostic","message":"unused import: `Listener`","code":{"code":"unused_imports","explanation":null},"level":"warning","spans":[{"file_name":"src\\core\\tcp_manager.rs","byte_start":1429,"byte_end":1437,"line_start":19,"line_end":19,"column_start":42,"column_end":50,"is_primary":true,"text":[{"text":"use tauri::{App<PERSON><PERSON><PERSON>, Emitter, Runtime, Listener};","highlight_start":42,"highlight_end":50}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"`#[warn(unused_imports)]` on by default","code":null,"level":"note","spans":[],"children":[],"rendered":null},{"message":"remove the unused import","code":null,"level":"help","spans":[{"file_name":"src\\core\\tcp_manager.rs","byte_start":1427,"byte_end":1437,"line_start":19,"line_end":19,"column_start":40,"column_end":50,"is_primary":true,"text":[{"text":"use tauri::{AppHandle, Emitter, Runtime, Listener};","highlight_start":40,"highlight_end":50}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: unused import: `Listener`\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\core\\tcp_manager.rs:19:42\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m19\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0muse tauri::{AppHandle, Emitter, Runtime, Listener};\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m| \u001b[0m\u001b[0m                                         \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m= \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15mnote\u001b[0m\u001b[0m: `#[warn(unused_imports)]` on by default\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused variable: `username`","code":{"code":"unused_variables","explanation":null},"level":"warning","spans":[{"file_name":"src\\core\\tcp_manager.rs","byte_start":25760,"byte_end":25768,"line_start":412,"line_end":412,"column_start":56,"column_end":64,"is_primary":true,"text":[{"text":"    pub async fn login(&self, addr: String, port: u16, username: String, password: String) -> Result<String> {","highlight_start":56,"highlight_end":64}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"`#[warn(unused_variables)]` on by default","code":null,"level":"note","spans":[],"children":[],"rendered":null},{"message":"if this is intentional, prefix it with an underscore","code":null,"level":"help","spans":[{"file_name":"src\\core\\tcp_manager.rs","byte_start":25760,"byte_end":25768,"line_start":412,"line_end":412,"column_start":56,"column_end":64,"is_primary":true,"text":[{"text":"    pub async fn login(&self, addr: String, port: u16, username: String, password: String) -> Result<String> {","highlight_start":56,"highlight_end":64}],"label":null,"suggested_replacement":"_username","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: unused variable: `username`\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\core\\tcp_manager.rs:412:56\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m412\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    pub async fn login(&self, addr: String, port: u16, username: String, password: String) -> Result<String> {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m| \u001b[0m\u001b[0m                                                       \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11mhelp: if this is intentional, prefix it with an underscore: `_username`\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m= \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15mnote\u001b[0m\u001b[0m: `#[warn(unused_variables)]` on by default\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused variable: `password`","code":{"code":"unused_variables","explanation":null},"level":"warning","spans":[{"file_name":"src\\core\\tcp_manager.rs","byte_start":25778,"byte_end":25786,"line_start":412,"line_end":412,"column_start":74,"column_end":82,"is_primary":true,"text":[{"text":"    pub async fn login(&self, addr: String, port: u16, username: String, password: String) -> Result<String> {","highlight_start":74,"highlight_end":82}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"if this is intentional, prefix it with an underscore","code":null,"level":"help","spans":[{"file_name":"src\\core\\tcp_manager.rs","byte_start":25778,"byte_end":25786,"line_start":412,"line_end":412,"column_start":74,"column_end":82,"is_primary":true,"text":[{"text":"    pub async fn login(&self, addr: String, port: u16, username: String, password: String) -> Result<String> {","highlight_start":74,"highlight_end":82}],"label":null,"suggested_replacement":"_password","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: unused variable: `password`\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\core\\tcp_manager.rs:412:74\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m412\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    pub async fn login(&self, addr: String, port: u16, username: String, password: String) -> Result<String> {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m| \u001b[0m\u001b[0m                                                                         \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11mhelp: if this is intentional, prefix it with an underscore: `_password`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"variant `InternalError` is never constructed","code":{"code":"dead_code","explanation":null},"level":"warning","spans":[{"file_name":"src\\errors.rs","byte_start":431,"byte_end":439,"line_start":10,"line_end":10,"column_start":10,"column_end":18,"is_primary":false,"text":[{"text":"pub enum AppError {","highlight_start":10,"highlight_end":18}],"label":"variant in this enum","suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src\\errors.rs","byte_start":2137,"byte_end":2150,"line_start":45,"line_end":45,"column_start":5,"column_end":18,"is_primary":true,"text":[{"text":"    InternalError(String), // 包含内部错误的描述","highlight_start":5,"highlight_end":18}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"`AppError` has a derived impl for the trait `Debug`, but this is intentionally ignored during dead code analysis","code":null,"level":"note","spans":[],"children":[],"rendered":null},{"message":"`#[warn(dead_code)]` on by default","code":null,"level":"note","spans":[],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: variant `InternalError` is never constructed\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\errors.rs:45:5\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m10\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0mpub enum AppError {\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m| \u001b[0m\u001b[0m         \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--------\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14mvariant in this enum\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m...\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m45\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    InternalError(String), // 包含内部错误的描述\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m| \u001b[0m\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^^^^^\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m= \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15mnote\u001b[0m\u001b[0m: `AppError` has a derived impl for the trait `Debug`, but this is intentionally ignored during dead code analysis\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m= \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15mnote\u001b[0m\u001b[0m: `#[warn(dead_code)]` on by default\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"call to `.clone()` on a reference in this situation does nothing","code":{"code":"noop_method_call","explanation":null},"level":"warning","spans":[{"file_name":"src\\main.rs","byte_start":4709,"byte_end":4717,"line_start":84,"line_end":84,"column_start":58,"column_end":66,"is_primary":true,"text":[{"text":"                let manager_clone = manager_state.inner().clone(); // 克隆 TcpManager (因为它实现了 Clone)","highlight_start":58,"highlight_end":66}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"the type `TcpManager<tauri_runtime_wry::Wry<EventLoopMessage>>` does not implement `Clone`, so calling `clone` on `&TcpManager<tauri_runtime_wry::Wry<EventLoopMessage>>` copies the reference, which does not do anything and can be removed","code":null,"level":"note","spans":[],"children":[],"rendered":null},{"message":"`#[warn(noop_method_call)]` on by default","code":null,"level":"note","spans":[],"children":[],"rendered":null},{"message":"remove this redundant call","code":null,"level":"help","spans":[{"file_name":"src\\main.rs","byte_start":4709,"byte_end":4717,"line_start":84,"line_end":84,"column_start":58,"column_end":66,"is_primary":true,"text":[{"text":"                let manager_clone = manager_state.inner().clone(); // 克隆 TcpManager (因为它实现了 Clone)","highlight_start":58,"highlight_end":66}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null},{"message":"if you meant to clone `TcpManager<tauri_runtime_wry::Wry<EventLoopMessage>>`, implement `Clone` for it","code":null,"level":"help","spans":[{"file_name":"src\\core\\tcp_manager.rs","byte_start":3220,"byte_end":3220,"line_start":49,"line_end":49,"column_start":1,"column_end":1,"is_primary":true,"text":[{"text":"pub struct TcpManager<R: Runtime> {","highlight_start":1,"highlight_end":1}],"label":null,"suggested_replacement":"#[derive(Clone)]\n","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: call to `.clone()` on a reference in this situation does nothing\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\main.rs:84:58\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m84\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m                let manager_clone = manager_state.inner().clone(); // 克隆 TcpManager (因为它实现了 Clone)\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m| \u001b[0m\u001b[0m                                                         \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m= \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15mnote\u001b[0m\u001b[0m: the type `TcpManager<tauri_runtime_wry::Wry<EventLoopMessage>>` does not implement `Clone`, so calling `clone` on `&TcpManager<tauri_runtime_wry::Wry<EventLoopMessage>>` copies the reference, which does not do anything and can be removed\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m= \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15mnote\u001b[0m\u001b[0m: `#[warn(noop_method_call)]` on by default\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14mhelp\u001b[0m\u001b[0m: remove this redundant call\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m84\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[38;5;9m- \u001b[0m\u001b[0m                let manager_clone = manager_state.inner()\u001b[0m\u001b[0m\u001b[38;5;9m.clone()\u001b[0m\u001b[0m; // 克隆 TcpManager (因为它实现了 Clone)\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m84\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[38;5;10m+ \u001b[0m\u001b[0m                let manager_clone = manager_state.inner(); // 克隆 TcpManager (因为它实现了 Clone)\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14mhelp\u001b[0m\u001b[0m: if you meant to clone `TcpManager<tauri_runtime_wry::Wry<EventLoopMessage>>`, implement `Clone` for it\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\core\\tcp_manager.rs:49:1\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m49\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[38;5;10m+ #[derive(Clone)]\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m50\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m| \u001b[0m\u001b[0mpub struct TcpManager<R: Runtime> {\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"5 warnings emitted","code":null,"level":"warning","spans":[],"children":[],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: 5 warnings emitted\u001b[0m\n\n"}
