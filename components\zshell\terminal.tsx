"use client";

import { useTheme } from "next-themes";
import { useEffect, useState, useRef } from "react";
import { listen } from "@tauri-apps/api/event";
import Editor from "@monaco-editor/react";

const EDITOR_OPTIONS = {
    minimap: { enabled: false },
    fontSize: 14,
    fontFamily: "var(--font-0xproto)",
    lineNumbers: "off",
    automaticLayout: true,
    formatOnPaste: true,
    formatOnType: true,
    padding: { top: 8, bottom: 8 },
    mouseWheelZoom: true,
    readOnly: true,
    domReadOnly: true,
    scrollBeyondLastLine: false,
    scrollbar: {
        vertical: 'visible',
        horizontal: 'visible',
        useShadows: false,
        verticalScrollbarSize: 10,
        horizontalScrollbarSize: 10
    }
} as const;

// TCP数据载荷类型
interface TcpDataPayload {
    id: string;
    board_id: string;
    data: string;
    timestamp: string;
}

interface TerminalProps {
    connectionId?: string | null;
    boardId?: string;
}

export function Terminal({ connectionId, boardId }: TerminalProps) {
    // 主题
    const { resolvedTheme } = useTheme();
    const editorTheme = resolvedTheme === "dark" ? "vs-dark" : "light";

    // 终端数据状态
    const [terminalData, setTerminalData] = useState<string>("");
    const editorRef = useRef<any>(null);

    // 监听TCP数据接收事件
    useEffect(() => {
        let unlisten: (() => void) | null = null;

        const setupListener = async () => {
            try {
                console.log(`[Terminal] 设置TCP数据监听器 - connectionId: ${connectionId}, boardId: ${boardId}`);
                unlisten = await listen<TcpDataPayload>('tcp_data_received', (event) => {
                    const payload = event.payload;
                    console.log(`[Terminal] 收到TCP数据事件:`, payload);

                    // 如果指定了connectionId，只处理匹配的连接数据
                    if (connectionId && payload.id !== connectionId) {
                        console.log(`[Terminal] 跳过不匹配的连接ID: ${payload.id} !== ${connectionId}`);
                        return;
                    }

                    // 如果指定了boardId，只处理匹配的单板数据
                    if (boardId && payload.board_id !== boardId) {
                        console.log(`[Terminal] 跳过不匹配的单板ID: ${payload.board_id} !== ${boardId}`);
                        return;
                    }

                    console.log(`[Terminal] 处理TCP数据: ${payload.data}`);

                    // 格式化时间戳
                    const timestamp = new Date(payload.timestamp).toLocaleTimeString();

                    // 将十六进制数据解码为文本
                    let displayData = payload.data;
                    try {
                        // 尝试将十六进制字符串解码为文本
                        const bytes = new Uint8Array(payload.data.match(/.{1,2}/g)?.map(byte => parseInt(byte, 16)) || []);
                        const decoder = new TextDecoder('utf-8', { fatal: false });
                        const decodedText = decoder.decode(bytes);
                        // 如果解码成功且包含可打印字符，使用解码后的文本
                        if (decodedText && /[\x20-\x7E]/.test(decodedText)) {
                            displayData = decodedText;
                        }
                    } catch (error) {
                        // 如果解码失败，保持原始十六进制数据
                        console.warn('解码十六进制数据失败:', error);
                    }

                    // 添加新数据到终端
                    const newLine = `[${timestamp}] ${payload.board_id ? `[${payload.board_id}] ` : ''}${displayData}\n`;
                    console.log(`[Terminal] 添加新行到终端: ${newLine.trim()}`);

                    setTerminalData(prev => prev + newLine);
                });
            } catch (error) {
                console.error('设置TCP数据监听器失败:', error);
            }
        };

        setupListener();

        return () => {
            console.log(`[Terminal] 清理TCP数据监听器 - connectionId: ${connectionId}, boardId: ${boardId}`);
            if (unlisten) {
                unlisten();
            }
        };
    }, [connectionId, boardId]);

    // 监听TCP连接关闭事件
    useEffect(() => {
        let unlisten: (() => void) | null = null;

        const setupListener = async () => {
            try {
                unlisten = await listen<TcpDataPayload>('tcp_closed', (event) => {
                    const payload = event.payload;

                    // 如果指定了connectionId，只处理匹配的连接
                    if (connectionId && payload.id !== connectionId) {
                        return;
                    }

                    const timestamp = new Date().toLocaleTimeString();
                    const newLine = `[${timestamp}] 连接已断开: ${payload.data}\n`;

                    setTerminalData(prev => prev + newLine);
                });
            } catch (error) {
                console.error('设置TCP关闭监听器失败:', error);
            }
        };

        setupListener();

        return () => {
            if (unlisten) {
                unlisten();
            }
        };
    }, [connectionId]);

    // 监听TCP错误事件
    useEffect(() => {
        let unlisten: (() => void) | null = null;

        const setupListener = async () => {
            try {
                unlisten = await listen<TcpDataPayload>('tcp_error', (event) => {
                    const payload = event.payload;

                    // 如果指定了connectionId，只处理匹配的连接
                    if (connectionId && payload.id !== connectionId) {
                        return;
                    }

                    const timestamp = new Date().toLocaleTimeString();
                    const newLine = `[${timestamp}] 错误: ${payload.data}\n`;

                    setTerminalData(prev => prev + newLine);
                });
            } catch (error) {
                console.error('设置TCP错误监听器失败:', error);
            }
        };

        setupListener();

        return () => {
            if (unlisten) {
                unlisten();
            }
        };
    }, [connectionId]);

    // 当有新数据时，自动滚动到底部
    useEffect(() => {
        if (editorRef.current) {
            const editor = editorRef.current;
            const model = editor.getModel();
            if (model) {
                const lineCount = model.getLineCount();
                editor.revealLine(lineCount);
            }
        }
    }, [terminalData]);

    // 当连接状态改变时，更新终端显示
    useEffect(() => {
        if (connectionId) {
            const timestamp = new Date().toLocaleTimeString();
            const message = boardId
                ? `[${timestamp}] 已连接到单板 ${boardId}\n`
                : `[${timestamp}] 已连接到服务器\n`;
            setTerminalData(message);
        } else {
            setTerminalData(""); // 未连接时不显示任何内容
        }
    }, [connectionId, boardId]);

    // 如果没有连接ID，显示提示信息
    if (!connectionId) {
        return (
            <div className="h-full flex items-center justify-center text-muted-foreground">
                <div className="text-center">
                    <p className="text-lg mb-2">没有活动的串口连接</p>
                    <p className="text-sm">请在左侧面板中点击"打开新的串口..."来创建连接</p>
                </div>
            </div>
        );
    }

    return (
        <Editor
            height="100%"
            language="txt"
            theme={editorTheme}
            value={terminalData}
            options={{
                ...EDITOR_OPTIONS,
            }}
            onMount={(editor) => {
                editorRef.current = editor;
            }}
        />
    );
}