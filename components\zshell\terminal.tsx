"use client";

import { useTheme } from "next-themes";
import { useEffect, useState, useRef } from "react";
import Editor from "@monaco-editor/react";
import { useTerminalData } from "./contexts/TerminalDataContext";

const EDITOR_OPTIONS = {
    minimap: { enabled: false },
    fontSize: 14,
    fontFamily: "var(--font-0xproto)",
    lineNumbers: "off",
    automaticLayout: true,
    formatOnPaste: true,
    formatOnType: true,
    padding: { top: 8, bottom: 8 },
    mouseWheelZoom: true,
    readOnly: true,
    domReadOnly: true,
    scrollBeyondLastLine: false,
    scrollbar: {
        vertical: 'visible',
        horizontal: 'visible',
        useShadows: false,
        verticalScrollbarSize: 10,
        horizontalScrollbarSize: 10
    }
} as const;



interface TerminalProps {
    connectionId?: string | null;
    boardId?: string;
}

export function Terminal({ connectionId, boardId }: TerminalProps) {
    // 主题
    const { resolvedTheme } = useTheme();
    const editorTheme = resolvedTheme === "dark" ? "vs-dark" : "light";

    // 全局终端数据管理
    const { getTerminalData, setTerminalData: setGlobalTerminalData } = useTerminalData();

    // 本地终端数据状态（从全局存储获取）
    const [terminalData, setTerminalData] = useState<string>("");
    const editorRef = useRef<any>(null);

    // 从全局存储获取终端数据
    useEffect(() => {
        if (!connectionId) {
            console.log(`[Terminal] 没有connectionId，清空数据`);
            setTerminalData("");
            return;
        }

        console.log(`[Terminal] 从全局存储获取数据 - connectionId: ${connectionId}, boardId: ${boardId}`);

        // 设置定时器定期更新数据（用于实时显示新消息）
        const interval = setInterval(() => {
            const newData = getTerminalData(connectionId, boardId);
            setTerminalData(prev => {
                if (prev !== newData) {
                    console.log(`[Terminal] 更新数据，长度从 ${prev.length} 到 ${newData.length}`);
                    return newData;
                }
                return prev;
            });
        }, 100); // 每100ms检查一次

        // 立即获取一次数据
        const initialData = getTerminalData(connectionId, boardId);
        setTerminalData(initialData);

        return () => {
            clearInterval(interval);
        };
    }, [connectionId, boardId, getTerminalData]);



    // 当有新数据时，自动滚动到底部
    useEffect(() => {
        if (editorRef.current) {
            const editor = editorRef.current;
            const model = editor.getModel();
            if (model) {
                const lineCount = model.getLineCount();
                editor.revealLine(lineCount);
            }
        }
    }, [terminalData]);

    // 当连接状态改变时，在全局存储中初始化连接消息
    useEffect(() => {
        if (connectionId) {
            const timestamp = new Date().toLocaleTimeString();
            const message = boardId
                ? `[${timestamp}] 已连接到单板 ${boardId}\n`
                : `[${timestamp}] 已连接到服务器\n`;

            // 检查是否已有数据，如果没有则初始化
            const existingData = getTerminalData(connectionId, boardId);
            if (!existingData) {
                console.log(`[Terminal] 初始化连接消息: ${message.trim()}`);
                setGlobalTerminalData(connectionId, boardId, message);
            }
        }
    }, [connectionId, boardId, getTerminalData, setGlobalTerminalData]);

    // 如果没有连接ID，显示提示信息
    if (!connectionId) {
        return (
            <div className="h-full flex items-center justify-center text-muted-foreground">
                <div className="text-center">
                    <p className="text-lg mb-2">没有活动的串口连接</p>
                    <p className="text-sm">请在左侧面板中点击"打开新的串口..."来创建连接</p>
                </div>
            </div>
        );
    }

    return (
        <Editor
            height="100%"
            language="txt"
            theme={editorTheme}
            value={terminalData}
            options={{
                ...EDITOR_OPTIONS,
            }}
            onMount={(editor) => {
                editorRef.current = editor;
            }}
        />
    );
}