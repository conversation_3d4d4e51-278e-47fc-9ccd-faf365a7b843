// hooks/usePersistence.ts
import { useEffect, useCallback, useRef } from 'react';
import { useToast } from "@/hooks/use-toast";
import { appConfigDir } from '@tauri-apps/api/path';
import { readTextFile, writeTextFile } from '@tauri-apps/plugin-fs';
import { invoke } from '@tauri-apps/api/core';

/**
 * 检查文件是否存在 (调用 Tauri 后端)
 * @param path 文件路径
 * @returns Promise<boolean> 文件是否存在
 */
const checkFileExists = async (path: string): Promise<boolean> => {
  try {
    return await invoke<boolean>('exists_file', { path });
  } catch (error) {
    console.error(`检查文件存在性失败 (${path}):`, error);
    return false; // 出错时默认文件不存在
  }
};

/**
 * 读取文本文件内容
 * @param filePath 文件路径
 * @returns Promise<string> 文件内容
 * @throws 如果读取失败会抛出错误
 */
const readFileContent = async (filePath: string): Promise<string> => {
  return readTextFile(filePath);
};

/**
 * 写入文本文件内容
 * @param filePath 文件路径
 * @param content 文件内容
 * @returns Promise<void>
 * @throws 如果写入失败会抛出错误
 */
const writeFileContent = async (filePath: string, content: string): Promise<void> => {
  try {
    // 确保文件路径有效
    if (!filePath || filePath.trim() === '') {
      throw new Error('无效的文件路径');
    }

    // 尝试写入文件
    await writeTextFile(filePath, content);

    // 验证文件是否写入成功
    const exists = await checkFileExists(filePath);
    if (!exists) {
      throw new Error(`文件写入后不存在: ${filePath}`);
    }
  } catch (error) {
    console.error(`写入文件失败:`, error);
    throw error; // 重新抛出错误以便上层处理
  }
};

interface UsePersistenceProps<T> {
  data: T;
  setData: React.Dispatch<React.SetStateAction<T>>;
  fileName: string;
  defaultData: T;
  validateData?: (data: any) => data is T;
}

/**
 * 通用持久化Hook
 * 
 * 负责在应用启动时加载数据，并在数据变化时保存到文件。
 */
export function usePersistence<T>({
  data,
  setData,
  fileName,
  defaultData,
  validateData,
}: UsePersistenceProps<T>) {
  const { toast } = useToast();
  const isLoadedRef = useRef(false); // 跟踪是否已完成初始加载

  /**
   * 获取状态文件的完整路径
   */
  const getStatePath = useCallback(async (): Promise<string> => {
    const configDir = await appConfigDir();
    return `${configDir}${fileName}`;
  }, [fileName]);

  /**
   * 保存当前数据到文件
   */
  const saveData = useCallback(async () => {
    // 如果尚未加载完成，则不执行保存，防止覆盖初始状态
    if (!isLoadedRef.current) {
      return;
    }

    try {
      const statePath = await getStatePath();
      console.log(`保存数据到: ${statePath}`);

      // 将数据写入文件
      await writeFileContent(statePath, JSON.stringify(data, null, 2));

    } catch (error) {
      toast({
        title: "保存数据失败",
        description: `${error}`,
        variant: "destructive",
        duration: 5000,
      });
    }
  }, [data, getStatePath, toast]);

  /**
   * 从文件加载数据
   */
  const loadData = useCallback(async () => {
    try {
      const statePath = await getStatePath();

      // 检查状态文件是否存在
      const stateFileExists = await checkFileExists(statePath);
      if (!stateFileExists) {
        isLoadedRef.current = true; // 标记为已加载（即使是默认状态）
        setData(defaultData);
        return;
      }

      // 读取状态文件
      const stateJson = await readFileContent(statePath);
      const parsedData = JSON.parse(stateJson);

      // 验证数据格式
      if (validateData && !validateData(parsedData)) {
        console.warn('加载的数据格式不正确，使用默认数据');
        setData(defaultData);
        isLoadedRef.current = true;
        return;
      }

      // 设置加载的数据
      setData(parsedData);

    } catch (error) {
      toast({
        title: "加载数据失败",
        description: `${error}`,
        variant: "destructive",
        duration: Infinity,
      });
      // 加载失败时使用默认数据
      setData(defaultData);
    } finally {
      isLoadedRef.current = true; // 标记加载过程完成（无论成功或失败）
    }
  }, [getStatePath, setData, defaultData, validateData, toast]);

  // --- Effects ---

  // Effect 1: 组件挂载时加载数据，只运行一次
  useEffect(() => {
    loadData();
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []); // 空依赖数组确保只运行一次

  // Effect 2: 监听数据变化，延迟保存
  useEffect(() => {
    // 只有在初始加载完成后才触发保存
    if (!isLoadedRef.current) {
      return;
    }

    // 使用 debounce 延迟保存
    const timer = setTimeout(() => {
      saveData();
    }, 500); // 延迟 500ms 保存

    return () => clearTimeout(timer); // 清理定时器
  }, [data, saveData]); // 依赖项包含 data 和 saveData

  // Effect 3: 应用关闭前保存数据
  useEffect(() => {
    const handleBeforeUnload = () => {
      console.log('应用即将关闭，尝试保存数据...');
      // 注意：beforeunload 中不能执行异步操作，Tauri 可能有特定 API 处理关闭事件
      // 最可靠的方式是在 Tauri Rust 端监听窗口关闭事件并触发保存
      // 这里的 saveData 调用可能不会完全执行或成功
      // 更好的做法是依赖 Effect 2 的延迟保存，或者使用 Tauri 的事件系统
    };

    window.addEventListener('beforeunload', handleBeforeUnload);

    return () => {
      window.removeEventListener('beforeunload', handleBeforeUnload);
    };
  }, [saveData]); // 依赖 saveData

  // 返回加载状态和手动保存函数
  return { 
    isLoaded: isLoadedRef.current,
    saveData: saveData,
    loadData: loadData
  };
}
