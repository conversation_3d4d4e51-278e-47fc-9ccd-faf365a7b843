[package]
name = "site-z"
version = "0.1.0"
description = "一个小工具"
authors = ["Wenjie"]
license = ""
repository = ""
edition = "2021"
rust-version = "1.77.2"

# See more keys and their definitions at https://doc.rust-lang.org/cargo/reference/manifest.html

[build-dependencies]
tauri-build = { version = "2.0.2", features = [] }

[dependencies]
serde_json = "1.0"
serde = { version = "1.0", features = ["derive"] }
tauri = { version = "2.1.1", features = ["test"] }
tauri-plugin-log = "2.0.0-rc"
tauri-plugin-dialog = "2.2.0"
tauri-plugin-fs = "2.2.0"
tokio = { version = "1", features = ["full"] } # full特性包含了net, io, sync, rt等
uuid = { version = "1", features = ["v4", "serde"] } # 用于生成唯一连接ID
chrono = "0.4" # 用于时间戳
log = "0.4"
simplelog = "0.12" # 一个简单的日志实现，可以写入文件
thiserror = "1.0" # 方便定义错误类型
hex = "0.4" # 用于将字节数据转换为十六进制字符串以便记录
dirs-next = "2.0"
