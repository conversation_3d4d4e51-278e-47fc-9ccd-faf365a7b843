import * as monaco from 'monaco-editor';

/**
 * 根据文件名获取对应的语言ID
 * 
 * @param filename 文件名或文件路径
 * @returns 匹配的语言ID，如果没有匹配则返回 'plaintext'
 */
export function getLanguageIdByFileName(filename: string): string {
  const languages = monaco.languages.getLanguages();
  const matchedLang = languages.find(lang => {
    // 检查文件名匹配（如 Dockerfile）
    if (lang.filenames?.some(f => f === filename)) return true;
    // 提取扩展名并匹配
    const extension = filename.slice(filename.lastIndexOf('.'));
    return lang.extensions?.some(ext => ext === extension);
  });
  return matchedLang?.id || 'plaintext';
}
