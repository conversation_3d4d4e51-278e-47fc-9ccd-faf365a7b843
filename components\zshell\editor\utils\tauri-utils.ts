// ./editor/utils/tauri-utils.ts
import { save as saveDialog } from '@tauri-apps/plugin-dialog';
import { open as openDialog } from '@tauri-apps/plugin-dialog';
import { readTextFile, writeTextFile } from '@tauri-apps/plugin-fs';
import { invoke } from '@tauri-apps/api/core';
import { appConfigDir } from '@tauri-apps/api/path';
import { EDITOR_STATE_FILE } from '../editor-constants'; // 引入常量

/**
 * 检查文件是否存在 (调用 Tauri 后端)
 * @param path 文件路径
 * @returns Promise<boolean> 文件是否存在
 */
export const checkFileExists = async (path: string): Promise<boolean> => {
  try {
    return await invoke<boolean>('exists_file', { path });
  } catch (error) {
    console.error(`检查文件存在性失败 (${path}):`, error);
    return false; // 出错时默认文件不存在
  }
};

/**
 * 获取编辑器状态文件的完整路径
 * @returns Promise<string> 状态文件的路径
 */
export const getEditorStatePath = async (): Promise<string> => {
  const configDir = await appConfigDir();
  return `${configDir}${EDITOR_STATE_FILE}`;
};

/**
 * 读取文本文件内容
 * @param filePath 文件路径
 * @returns Promise<string> 文件内容
 * @throws 如果读取失败会抛出错误
 */
export const readFileContent = async (filePath: string): Promise<string> => {
  return readTextFile(filePath);
};

/**
 * 写入文本文件内容
 * @param filePath 文件路径
 * @param content 文件内容
 * @returns Promise<void>
 * @throws 如果写入失败会抛出错误
 */
export const writeFileContent = async (filePath: string, content: string): Promise<void> => {
  try {
    // 确保文件路径有效
    if (!filePath || filePath.trim() === '') {
      throw new Error('无效的文件路径');
    }

    // 尝试写入文件
    await writeTextFile(filePath, content);

    // 验证文件是否写入成功
    const exists = await checkFileExists(filePath);
    if (!exists) {
      throw new Error(`文件写入后不存在: ${filePath}`);
    }
  } catch (error) {
    console.error(`写入文件失败:`, error);
    throw error; // 重新抛出错误以便上层处理
  }
};

/**
 * 打开文件保存对话框
 * @param defaultPath 默认路径 (可选)
 * @returns Promise<string | null> 用户选择的保存路径，如果取消则为 null
 */
export const showSaveFileDialog = async (defaultPath?: string): Promise<string | null> => {
  return saveDialog({
    defaultPath: defaultPath,
    filters: [{
      name: 'Text Files',
      extensions: ['txt', 'md'] // 可以根据需要扩展
    }],
    // 注意：Tauri v1 的 save API 可能不直接支持 defaultName，需要用户手动输入
    // 如果使用的是 Tauri v2 或更高版本，请查阅其 API 文档
  });
};

/**
 * 打开文件选择对话框
 * @returns Promise<string | null> 用户选择的文件路径，如果取消则为 null
 */
export const showOpenFileDialog = async (): Promise<string | null> => {
    const selected = await openDialog({
        multiple: false,
        filters: [{
            name: 'Script Files',
            extensions: ['txt', 'md'] // 可以根据需要扩展
        }]
    });
    // Tauri v1 open 返回 string | string[] | null
    // Tauri v2 open 返回 string[] | null
    // 这里我们强制只处理单文件选择
    if (Array.isArray(selected)) {
        return selected[0] ?? null;
    }
    return selected ?? null;
};
