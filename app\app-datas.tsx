// lucide图标
import {
    Image,
    AlertTriangle,
  } from "lucide-react"

export const route_datas = {
  conn_types: [
    // 默认为空，单板连接将通过 NavBoards 组件动态添加
  ],
  apps: [
    {
      title: "画图",
      path: "/draw",
      icon: Image,
    },
    {
      title: "错误测试",
      path: "/error-test",
      icon: AlertTriangle,
    },
  ]
};

export const envs_datas = [
    {
      name: "5261h",
      addr: "127.0.0.1",
    },
    {
      name: "3764a",
      addr: "186.12.7.1",
    },
    {
      name: "d752",
      addr: "131.46.7.1",
    },
];