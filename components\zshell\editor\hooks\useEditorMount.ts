// ./components/zshell/editor/hooks/useEditorMount.ts
import { useCallback, MutableRefObject } from 'react';
import type { editor } from 'monaco-editor';

/**
 * Hook 属性接口
 */
interface UseEditorMountProps {
  editorsRef: MutableRefObject<Record<string, editor.IStandaloneCodeEditor>>;
  activeTabId: string | null;
  setActiveTabId: (id: string) => void;
  initEditorActions: (editor: editor.IStandaloneCodeEditor) => void;
  applyHighlightsToEditor: () => void;
}

/**
 * Hook: 管理编辑器实例的挂载逻辑
 *
 * 负责处理编辑器实例的挂载、引用存储、快捷键添加和事件监听等
 */
export function useEditorMount({
  editorsRef,
  activeTabId,
  setActiveTabId,
  initEditorActions,
  applyHighlightsToEditor
}: UseEditorMountProps) {

  /**
   * Monaco Editor 实例挂载时的回调函数
   */
  const handleEditorMount = useCallback((
    editorInstance: editor.IStandaloneCodeEditor,
    mountedTabId: string // 传入挂载的 Tab ID
  ) => {
    editorsRef.current[mountedTabId] = editorInstance; // 存储编辑器实例引用

    // 为新挂载的编辑器添加快捷键操作
    console.log(`为编辑器添加快捷键操作: ${mountedTabId}`);
    try {
      initEditorActions(editorInstance);
    } catch (error) {
      console.error('添加编辑器操作时出错:', error);
    }

    // 应用当前的高亮状态到新挂载的编辑器
    applyHighlightsToEditor(); // 调用高亮 Hook 的函数

    // 可选：编辑器获得焦点时，确保它是活动标签页
    editorInstance.onDidFocusEditorWidget(() => {
      if (activeTabId !== mountedTabId) {
        setActiveTabId(mountedTabId);
      }
    });

    // 可选：监听编辑器卸载，清理引用
    editorInstance.onDidDispose(() => {
      delete editorsRef.current[mountedTabId];
      // 清理高亮 Hook 中的相关装饰器引用（如果高亮 Hook 内部没有自动处理）
      // clearDecorationsForTab(mountedTabId); // 假设有这样的函数
    });

  }, [initEditorActions, applyHighlightsToEditor, activeTabId, setActiveTabId, editorsRef]);

  return { handleEditorMount };
}
