import { useState, useCallback } from 'react';
import type { Tab } from '../types';
import { getLanguageIdByFileName } from '../utils/monaco-utils';

// // 默认的初始标签页 (如果加载状态失败或无状态时使用)
// const createDefaultTab = (): Tab => ({
//   id: Date.now().toString(),
//   title: "untitled.md", // 默认给一个 .md 后缀
//   content: "",
//   language: "markdown",
//   filePath: undefined,
//   isSaved: true, // 新建的空标签视为已保存
// });

/**
 * Hook: 管理编辑器标签页状态
 *
 * 负责标签页的增、删、切换、内容更新等核心逻辑。
 */
export function useTabsManager(initialTabs: Tab[] = []) {
  const [tabs, setTabs] = useState<Tab[]>(initialTabs.length > 0 ? initialTabs : []);
  const [activeTabId, setActiveTabId] = useState<string | null>(
    initialTabs.length > 0 ? initialTabs[0].id : tabs[0]?.id ?? null
  );

  /**
   * 添加一个新的标签页
   * @param filePath 文件路径 (可选)
   * @param content 文件内容
   * @param options 可选配置 { activate: 是否立即激活新标签页 }
   * @returns 新创建的标签页的 ID
   */
  const addTab = useCallback((
    filePath: string | undefined,
    content: string,
    options: { activate?: boolean } = { activate: true }
  ): string => {
    const fileName = filePath ? filePath.split(/[\\/]/).pop() || 'Untitled' : `untitled-${Date.now()}.txt`;
    const newTab: Tab = {
      id: Date.now().toString(), // 使用时间戳确保唯一性
      title: fileName,
      content,
      language: getLanguageIdByFileName(fileName),
      filePath: filePath,
      isSaved: !!filePath, // 有 filePath 视为已保存 (刚打开时)，否则视为未保存
    };

    setTabs(prev => [...prev, newTab]);
    if (options.activate) {
      setActiveTabId(newTab.id);
    }
    return newTab.id;
  }, []);

  /**
   * 关闭指定的标签页
   * @param tabId 要关闭的标签页 ID
   */
  const closeTab = useCallback((tabId: string) => {
    setTabs(prevTabs => {
      const indexToClose = prevTabs.findIndex(tab => tab.id === tabId);
      if (indexToClose === -1) return prevTabs; // 找不到则不处理

      const newTabs = prevTabs.filter(tab => tab.id !== tabId);

      // 如果关闭的是当前活动标签页，需要确定新的活动标签页
      if (activeTabId === tabId) {
        let newActiveId: string | null = null;
        if (newTabs.length > 0) {
          // 优先激活前一个标签页，如果关闭的是第一个，则激活新的第一个
          newActiveId = newTabs[Math.max(0, indexToClose - 1)].id;
        }
        setActiveTabId(newActiveId);
      }
    //   // 如果关闭后没有标签页了，创建一个默认的空标签页
    //   if (newTabs.length === 0) {
    //       const defaultTab = createDefaultTab();
    //       setActiveTabId(defaultTab.id);
    //       return [defaultTab];
    //   }

      return newTabs;
    });
  }, [activeTabId]);

  /**
   * 更新指定标签页的内容
   * @param tabId 标签页 ID
   * @param newContent 新内容
   */
  const updateTabContent = useCallback((tabId: string, newContent: string) => {
    setTabs(prev => {
      const updatedTabs = prev.map(tab =>
        tab.id === tabId
          ? { ...tab, content: newContent, isSaved: false } // 内容改变，标记为未保存
          : tab
      );

      return updatedTabs;
    });
  }, []);

  /**
   * 更新指定标签页的文件路径和标题 (通常在首次保存后调用)
   * @param tabId 标签页 ID
   * @param filePath 新的文件路径
   * @param title 新的标题
   */
  const updateTabFileInfo = useCallback((tabId: string, filePath: string, title: string) => {
    setTabs(prev => {
      const updatedTabs = prev.map(tab =>
        tab.id === tabId
          ? {
              ...tab,
              filePath, // 更新文件路径
              title,    // 更新标题
              isSaved: true, // 标记为已保存
              language: getLanguageIdByFileName(title) // 更新语言
            }
          : tab
      );

      
      return updatedTabs;
    });
  }, []);

   /**
   * 将指定标签页标记为已保存状态
   * @param tabId 标签页 ID
   */
  const markTabAsSaved = useCallback((tabId: string) => {
    setTabs(prev => {
      const updatedTabs = prev.map(tab =>
        tab.id === tabId ? { ...tab, isSaved: true } : tab
      );
      return updatedTabs;
    });
  }, []);

  /**
   * 设置当前活动的标签页
   * @param tabId 活动标签页的 ID
   */
  const setActiveTab = useCallback((tabId: string | null) => {
    setActiveTabId(tabId);
  }, []);

  // // 获取当前活动的 Tab 对象
  // const activeTabData = tabs.find(tab => tab.id === activeTabId) ?? null;

  return {
    tabs,
    activeTabId,
    // activeTabData,
    addTab,
    closeTab,
    updateTabContent,
    updateTabFileInfo,
    markTabAsSaved,
    setActiveTab,
    setTabs, // 暴露 setTabs 以便持久化 Hook 可以直接设置初始状态
    setActiveTabId, // 暴露 setActiveTabId 以便持久化 Hook 可以直接设置初始状态
  };
}