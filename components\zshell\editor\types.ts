import type { editor } from 'monaco-editor';

/**
 * 编辑器标签页接口定义
 */
export interface Tab {
  id: string;          // 唯一标识符
  title: string;       // 标签页标题 (通常是文件名)
  content: string;     // 文件内容
  language?: string;    // Monaco 编辑器语言 ID
  filePath?: string;    // 文件在磁盘上的绝对路径 (可选)
  isSaved: boolean;     // 文件是否已保存
  editor?: editor.IStandaloneCodeEditor; // 关联的 Monaco 编辑器实例 (可选, 运行时关联)
  decorations?: editor.IEditorDecorationsCollection; // 关联的 Monaco 装饰器 (可选, 运行时关联)
}

/**
 * 用于持久化的编辑器状态接口 (简化版)
 */
export interface EditorState {
  tabs: { filePath: string; title: string; id: string }[]; // 只保存有路径的标签页信息
  activeTabId: string | null; // 活动标签页的 ID
}

/**
 * 标签页基本信息接口 (用于 props 传递)
 */
export interface TabBasicInfo {
  id: string;
  filePath?: string;
}