## rust后端连接部分代码编写
我的应用中，关于TCP连接的部分，需实现：
1. 有一个`tauri::command`用来Ping一个TCP服务器，返回是否可Ping通；
2. 有一个`tauri::command`用来连接TCP服务器。这个接口的入参是地址和端口号。这个接口可以多次调用以同时连接多个服务器；
3. 对于每个连接，需要有一个`tauri::command`用来发送数据。这个接口的入参是连接的标识符和要发送的数据；同时，对于每个连接，当收到数据时，需要能主动发送给前端。同一个连接，发送和接收数据是异步的。发送和接收的所有数据都要按一定格式记录日志保存到本地文件中（比如时间戳、连接标识符、数据方向、数据内容等）；
4. 对于每个连接，需要有一个`tauri::command`用来断开连接。这个接口的入参是连接的标识符；
5. 核心逻辑与`tauri::command`要相互分离，放到不同的文件中，同时为核心逻辑编写完善的单元测试。

请基于你刚刚提到的最佳实践，帮我实现上述内容（我的Tauri版本是2.0的，上面涉及的异步操作，请使用tokio来实现），谢谢！注释记得用中文哈！

## 前端用后端的TCP接口
请帮我修改我的Tauri应用：
1. 改造一下components\app-sidebar\env-switcher.tsx，用户通过这个组件来管理和新增环境信息。用户可以点击其中的环境信息（CommandItem），来连接一个环境。连接成功与否，弹出一个Toast。若连接成功，则在Terminal的Editor的value里显示服务器发给我们的东西。而用户可以通过按F7，将脚本界面的光标行内容发送给连接的服务器。
2. 若用户切换了一个环境（点击了另一个CommandItem），则需要把当前已连接的服务器断开，再连接新的服务器。
3. 用户新增环境信息时，他们会点开title为“新环境”的那个Dialog，然后在里面输入环境的名称、ip地址端口，以及密码（密码这个可以先不跟后端交互，因为当前密码验证的后端验证接口还没写）。用户输入ip地址端口时，需要调用一下后端我写的ping函数，来查看一下可否ping通，并在输入框右边显示一下ping的结果给用户看。他们输入完后，会点击右下角的“连接”按钮，直接连接服务器。同时，他们本次输入的环境信息，也会自动保存到CommandItem列表里，以供下次选择连接用。
4. 环境信息的存储，也要想当前已实现的编辑器状态那样，持久化到文件里。