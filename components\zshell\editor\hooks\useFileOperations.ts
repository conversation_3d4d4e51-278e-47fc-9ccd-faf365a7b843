// ./components/zshell/editor/hooks/useFileOperations.ts
import { useCallback } from 'react';
import { useToast } from "@/hooks/use-toast";
import type { Tab } from '../types';
import {
  showSaveFileDialog,
  readFileContent,
  writeFileContent,
  showOpenFileDialog
} from '../utils/tauri-utils';

interface UseFileOperationsProps {
  addTab: (filePath: string | undefined, content: string) => void;
  setActiveTabId: (id: string) => void;
  updateTabFileInfo: (tabId: string, filePath: string, title: string) => void;
  markTabAsSaved: (tabId: string) => void;
}

/**
 * Hook: 处理编辑器文件操作
 *
 * 负责文件的打开、保存等操作，与Tauri文件系统交互
 */
export function useFileOperations({
  addTab,
  setActiveTabId,
  updateTabFileInfo,
  markTabAsSaved,
}: UseFileOperationsProps) {
  const { toast } = useToast();

  /**
   * 保存当前活动标签页的文件
   * @param forceContent 强制使用的内容，如果提供则使用这个内容而不是活动标签页的内容
   * @returns Promise<boolean> 保存是否成功
   */
  const saveActiveFile = useCallback(async (tabs: Tab[], activeTabId: string | null): Promise<boolean> => {
    if (!activeTabId) {
      toast({ description: "没有活动的标签页可保存", variant: "default", duration: 2000 });
      return false;
    }

    // 获取当前活动标签页数据
    const activeTab = tabs.find(tab => tab.id === activeTabId);
    if (!activeTab) {
      toast({ description: "找不到活动标签页", variant: "destructive", duration: 2000 });
      return false;
    }

    // 使用最新的文件路径
    let filePath = activeTab.filePath;
    try {
      if (!filePath) {
        // 如果文件没有路径 (新文件)，弹出保存对话框获取路径
        const savePath = await showSaveFileDialog(activeTab.title);
        if (!savePath) {
          toast({ description: "取消保存", variant: "default", duration: 2000 });
          return false;       // 用户取消保存
        }
        filePath = savePath;  // 用户选择了保存路径
      }

      const fileName = filePath.split(/[\\/]/).pop() || 'Untitled';

      // 先写入文件
      await writeFileContent(filePath, activeTab.content);

      // 然后更新标签页信息
      updateTabFileInfo(activeTabId, filePath, fileName);

      // 标记为已保存
      markTabAsSaved(activeTabId);

      toast({
        title: "保存成功",
        description: `文件 ${fileName} 已保存`,
        variant: "default",
        duration: 2000,
      });

      return true;
    } catch (error) {
      toast({
        title: "保存失败",
        description: `保存文件时出错: ${error}`,
        variant: "destructive",
        duration: Infinity,
      });
      return false;

    }
  }, [toast, updateTabFileInfo, markTabAsSaved]);

  /**
   * 处理打开文件按钮点击事件 (由 AddTabBtn_OpenFile 调用)
   * @param filePath 选择的文件路径
   * @param content 读取到的文件内容
   */
  const handleOpenFile = useCallback((tabs: Tab[], filePath: string, content: string) => {
    // 检查文件是否已在标签页中打开
    const existingTab = tabs.find(tab => tab.filePath === filePath);
    if (existingTab) {
      setActiveTabId(existingTab.id); // 切换到已存在的标签页
      toast({ description: `切换到已打开的文件: ${existingTab.title}`, duration: 1500 });
    } else {
      addTab(filePath, content); // 添加新标签页
    }
  }, [addTab, setActiveTabId, toast]);

  /**
   * 处理空编辑器提示中的打开文件操作
   */
  const handleOpenFileFromPrompt = useCallback(async (tabs: Tab[]) => {
    try {
      const selectedPath = await showOpenFileDialog();
      if (selectedPath) {
        // 检查是否已打开
        const existingTab = tabs.find(tab => tab.filePath === selectedPath);
        if (existingTab) {
          setActiveTabId(existingTab.id);
          toast({ description: `切换到已打开的文件: ${existingTab.title}`, duration: 1500 });
          return;
        }
        // 读取并添加
        const contents = await readFileContent(selectedPath);
        addTab(selectedPath, contents);
      }
    } catch (error) {
      toast({
        title: "打开文件失败",
        description: `${error}`,
        variant: "destructive",
        duration: Infinity,
      });
    }
  }, [addTab, setActiveTabId, toast]);

  return {
    saveActiveFile,
    handleOpenFile,
    handleOpenFileFromPrompt
  };
}
