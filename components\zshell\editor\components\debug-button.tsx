"use client";

import { Bug } from "lucide-react";
import { Popover, PopoverContent, PopoverTrigger } from "@/components/ui/popover";
import type { Tab } from '../types';

// 调试按钮属性接口
export interface DebugButtonProps {
  tabs: Tab[];
  activeTabId: string | null;
}

/**
 * 调试信息按钮组件
 * 
 * 点击后显示一个包含tabs和activeTabId信息的弹出面板
 */
export const DebugButton = ({ tabs, activeTabId }: DebugButtonProps) => {
  return (
    <Popover>
      <PopoverTrigger asChild>
        <button
          type="button"
          title="调试信息"
          className="h-9 px-2 bg-background hover:bg-muted text-muted-foreground hover:text-foreground transition-colors"
        >
          <Bug className="h-4 w-4" />
        </button>
      </PopoverTrigger>
      <PopoverContent className="w-96 max-h-[400px] overflow-auto">
        <div className="space-y-4">
          <div>
            <h4 className="font-medium mb-1">当前活动标签页ID:</h4>
            <pre className="bg-muted p-2 rounded text-xs overflow-x-auto">
              {activeTabId || "无"}
            </pre>
          </div>
          
          <div>
            <h4 className="font-medium mb-1">标签页列表:</h4>
            <pre className="bg-muted p-2 rounded text-xs overflow-x-auto">
              {JSON.stringify(tabs, (key, value) => {
                // 排除editor和decorations属性，它们包含循环引用
                if (key === 'editor' || key === 'decorations') {
                  return '[对象引用]';
                }
                return value;
              }, 2)}
            </pre>
          </div>
        </div>
      </PopoverContent>
    </Popover>
  );
};
