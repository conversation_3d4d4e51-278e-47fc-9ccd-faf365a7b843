// 添加测试环境需要的全局设置
import '@testing-library/jest-dom';

// 模拟 monaco-editor
jest.mock('monaco-editor', () => {
  return {
    editor: {
      TrackedRangeStickiness: {
        NeverGrowsWhenTypingAtEdges: 1
      },
      KeyMod: {
        CtrlCmd: 2048
      },
      KeyCode: {
        KeyS: 83,
        F8: 119
      }
    }
  };
});

// 模拟 window 对象
Object.defineProperty(window, 'matchMedia', {
  writable: true,
  value: jest.fn().mockImplementation(query => ({
    matches: false,
    media: query,
    onchange: null,
    addListener: jest.fn(),
    removeListener: jest.fn(),
    addEventListener: jest.fn(),
    removeEventListener: jest.fn(),
    dispatchEvent: jest.fn(),
  })),
});

// 模拟 document.head
if (!document.head) {
  Object.defineProperty(document, 'head', {
    value: {
      appendChild: jest.fn(),
    }
  });
}
