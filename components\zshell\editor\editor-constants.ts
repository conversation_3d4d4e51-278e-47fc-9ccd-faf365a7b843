/**
 * 编辑器相关常量
 */

// 高亮相关常量
export const MAX_HIGHLIGHTS = 1000;
export const HIGHLIGHT_CLASS_PREFIX = 'custom-highlight';

// 编辑器状态文件路径
export const EDITOR_STATE_FILE = '.editor-state.json';

// 默认编辑器选项
export const EDITOR_OPTIONS = {
  minimap: { enabled: false },
  fontSize: 14,
  fontFamily: "var(--font-go-mono), var(--font-source-han-serif)",
  lineNumbers: "on",
  scrollBeyondLastLine: false,
  automaticLayout: true,
  formatOnPaste: true,
  formatOnType: true,
  padding: { top: 8, bottom: 8 },
  mouseWheelZoom: true,
  scrollbar: {
    vertical: 'visible',
    horizontal: 'visible',
    useShadows: false,
    verticalScrollbarSize: 10,
    horizontalScrollbarSize: 10
  },
  // 禁用内置的保存快捷键，使用我们自定义的保存处理
  overrideServices: {
    'editor.saveHandler': null
  }
} as const;

// 高亮颜色
export const HIGHLIGHT_COLORS = [
  "#7f1d1d", "#064e3b", "#134e4a",
  "#164e63", "#312e81", "#500724",
  "#4a044e", "#ca8a04", "#111827",
  "#083344", "#fb923c", "#65a30d",
  "#10b981", "#06b6d4"
];
