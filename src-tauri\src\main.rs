// src/main.rs

// 这行代码用于在 Windows 平台的发布 (release) 版本中阻止显示一个额外的控制台窗口。
// 在调试 (debug) 版本中，控制台窗口通常是可见的。
// DO NOT REMOVE!!
#![cfg_attr(not(debug_assertions), windows_subsystem = "windows")]

// 声明项目中的其他模块，使得可以在 main.rs 中访问它们。
mod commands; // 包含Tauri命令的实现
mod core;     // 包含核心逻辑，如 tcp_manager 和 logger
mod errors;   // 包含我们刚看过的 AppError 定义
mod models;   // 包含数据模型，如 TcpDataPayload

// 从我们自己的模块和 tauri crate 中导入必要的项。
use crate::core::tcp_manager::TcpManager;
use tauri::{Manager, Listener, Wry}; // Wry 是 Tauri 默认的 Web 渲染引擎类型。
use std::fs;

fn main() {
    // 步骤 1: 设置日志文件路径
    let log_file_path = match dirs_next::data_local_dir() { // 或者 dirs_next::cache_dir()
        Some(mut path) => {
            path.push("site-Z"); // 为你的应用创建一个子目录
            if !path.exists() {
                fs::create_dir_all(&path).expect("无法创建应用日志目录");
            }
            path.push("app_debug.log");
            path
        }
        None => {
            // Fallback if system directory can't be determined
            // (可以退回到项目根目录的 dev_logs，或者直接 panic)
            eprintln!("无法获取系统本地数据目录，将使用临时路径。");
            let current_directory_pathbuf = std::env::current_dir()
                .expect("无法获取当前目录 (src-tauri)");
            let project_root_path_ref: &std::path::Path = current_directory_pathbuf
                .parent()
                .expect("无法获取项目根目录");
            let log_dir = project_root_path_ref.join("dev_logs_fallback");
            if !log_dir.exists() {
                fs::create_dir_all(&log_dir).expect("无法创建 dev_logs_fallback 目录");
            }
            log_dir.join("app_tcp_debug_fallback.log")
        }
    };

    // 步骤 2: 初始化日志系统
    // 调用我们之前定义的 core::logger::init_logger 函数。
    // log_file_path.clone() 因为 init_logger 需要拥有路径的所有权 (PathBuf)，
    // 而我们后面还想打印这个路径。
    if let Err(e) = core::logger::init_logger(log_file_path.clone()) {
        // 如果日志初始化失败，向标准错误输出打印错误信息。
        // 注意：此时日志系统本身可能还未成功初始化，所以这条 eprintln 很重要。
        // {:?} 使用 Debug trait 来格式化错误 e 和路径。
        eprintln!("日志初始化失败: {:?}, 日志路径: {:?}", e, log_file_path);
        // 在实际应用中，如果日志是关键功能，这里可能会选择退出程序。
        // std::process::exit(1);
    } else {
        println!("应用启动，日志文件位于: {:?}", log_file_path);
    }

    // 步骤 3: 构建并运行 Tauri 应用
    tauri::Builder::default() // 创建一个默认的 Tauri 应用构建器。
        .plugin(tauri_plugin_fs::init())
        .plugin(tauri_plugin_dialog::init())
        .setup(|app| { // .setup() 允许在应用启动时执行一些初始化代码。
                       // app 参数是 tauri::App 的引用。
            let app_handle = app.handle().clone(); // 获取应用句柄 (AppHandle)，用于与 Tauri 核心交互。
                                                            // AppHandle 是轻量级的，可以克隆。
            // 创建 TcpManager 实例，并将应用句柄传递给它。
            let tcp_manager = TcpManager::<Wry>::new(app_handle.clone());
            // 将 TcpManager 实例注册为 Tauri 的托管状态 (managed state)。
            // 这样，在 Tauri 命令中就可以通过 app.state::<TcpManager>() 来访问它。
            app.manage(tcp_manager);

            // 为关闭事件设置监听器，以实现优雅关闭。
            let app_handle_clone_for_shutdown = app.handle().clone();
            // 监听 "tauri://close-requested" 事件，这个事件在用户尝试关闭窗口时触发。
            app.listen("tauri://close-requested", move |_event| {
                log::info!("接收到 tauri://close-requested 事件，准备关闭TCP连接。");
                // 从 Tauri 状态中获取 TcpManager 的引用。
                let manager_state: tauri::State<TcpManager<Wry>> = app_handle_clone_for_shutdown.state();
                // .inner() 获取被 tauri::State 包裹的实际 TcpManager 实例的引用。
                let manager_clone = manager_state.inner().clone(); // 克隆 TcpManager (因为它实现了 Clone)

                // 创建一个新的 Tokio 运行时来执行异步的 shutdown_all。
                // 这是因为 Tauri 的事件回调通常是同步的，而 shutdown_all 是异步的。
                // .expect() 如果创建运行时失败则 panic。
                let rt = tokio::runtime::Runtime::new().expect("无法创建Tokio运行时");
                // rt.block_on() 会阻塞当前线程，直到提供的异步代码块执行完毕。
                rt.block_on(async move {
                    manager_clone.shutdown_all().await; // 调用 TcpManager 的方法关闭所有连接。
                });
                log::info!("TCP连接清理完成。");
                // 注意：这里没有调用 app.exit(0) 或类似的东西。
                // Tauri 默认行为是，在 "tauri://close-requested" 的所有监听器执行完毕后，
                // 如果没有监听器取消事件，应用会继续关闭流程。
            });

            Ok(()) // setup 闭包需要返回 Result<(), Box<dyn std::error::Error>>。Ok(()) 表示成功。
        })
        .invoke_handler(tauri::generate_handler![ // 注册后端命令，使其可以从前端 JavaScript 调用。
            commands::ping_tcp_server,
            commands::connect_tcp_server,
            commands::login_tcp_server,
            commands::send_tcp_data,
            commands::send_tcp_data_board,
            commands::disconnect_tcp_server,
            commands::exists_file
        ])
        .run(tauri::generate_context!()) // 运行 Tauri 应用。generate_context!() 会在编译时处理 tauri.conf.json。
        .expect("运行Tauri应用时出错"); // 如果 run 方法返回错误 (例如，无法启动 webview)，则 panic。
}