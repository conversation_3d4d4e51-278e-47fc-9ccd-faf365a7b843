"use client"

import React, { Suspense, lazy } from 'react';

// 全局样式
import "./globals.css";

// 字体
import { getFontVariables } from "./fonts";

// 路由组件
import { BrowserRouter as Router, Routes, Route, useLocation, useParams } from 'react-router-dom';

// shadcn/ui组件
import { ThemeProvider } from "@/components/theme-switcher/theme-provider"
import { AppSidebar } from "@/components/app-sidebar/app-sidebar"
import { SidebarProvider } from "@/components/ui/sidebar"
import { Toaster } from "@/components/ui/toaster";
import { ToastProvider } from "@/components/ui/toast";
import { LoadingFallback } from "@/components/ui/loading-fallback";
import { TooltipProvider } from "@/components/ui/tooltip";

// 应用组件
import { WindowBtnsGroup } from "@/components/window-btn";
import { TerminalDataProvider } from "@/components/zshell/contexts/TerminalDataContext";

// 主面板 - 使用懒加载
const ZShell = lazy(() => import("@/components/zshell/zshell").then(module => ({
  default: module.ZShell  // React.lazy 要求 Promise resolve 一个带有 default 属性的对象，该属性指向一个 React 组件
})));

// 带参数的ZShell包装组件
function ZShellWithParams() {
  const { connectionId, boardId } = useParams<{ connectionId: string; boardId: string }>();
  return <ZShell connectionId={connectionId} boardId={boardId} />;
}

// 错误测试组件 - 使用懒加载
const ErrorTestComponent = lazy(() => import("@/components/test/error-test"));

// 应用数据
import { route_datas } from "@/app/app-datas";

// 错误边界组件
interface ErrorBoundaryState {
  hasError: boolean;
  error: Error | null;
}

interface ErrorBoundaryProps {
  children: React.ReactNode;
}

class ErrorBoundary extends React.Component<ErrorBoundaryProps, ErrorBoundaryState> {
  constructor(props: ErrorBoundaryProps) {
    super(props);
    this.state = { hasError: false, error: null };
  }

  static getDerivedStateFromError(error: Error): ErrorBoundaryState {
    return { hasError: true, error };
  }

  componentDidCatch(error: Error, errorInfo: React.ErrorInfo): void {
    console.error("应用错误:", error, errorInfo);
  }

  render(): React.ReactNode {
    if (this.state.hasError) {
      return (
        <div className="p-6 bg-destructive/10 rounded-lg m-4 border border-destructive">
          <h2 className="text-xl font-bold mb-2">应用发生错误 o_O</h2>
          <p className="mb-4">请联系开发者</p>
          <pre className="bg-card p-2 rounded text-sm overflow-auto max-h-[300px]">
            {this.state.error?.toString()}
          </pre>
          <button
            type="button"
            className="mt-4 px-4 py-2 bg-primary text-primary-foreground rounded-md hover:bg-primary/90"
            onClick={() => this.setState({ hasError: false, error: null })}
          >
            尝试恢复
          </button>
        </div>
      );
    }

    return this.props.children;
  }
}

// 路由转场动画组件
function RouteTransition() {
  const location = useLocation();

  // 这里可以根据 location 变化添加转场动画逻辑
  React.useEffect(() => {
    // 简单的路由变化日志
    console.log('路由变化:', location.pathname);
  }, [location]);

  return null;
}

// 主应用内容组件
function AppContent() {
  return (
    <Router>
      <AppSidebar/>
      <main className="w-full h-screen overflow-hidden">
        <Suspense fallback={<LoadingFallback />}>
          <Routes>
            <Route key="/" path="/" element={<ZShell title="sIte-z" />} />
            {/* 错误测试路由 */}
            <Route key="/error-test" path="/error-test" element={<ErrorTestComponent />} />
            {/* 动态单板路由 */}
            <Route key="/terminal/:connectionId/:boardId" path="/terminal/:connectionId/:boardId" element={<ZShellWithParams />} />
            {/* 其他路由 */}
            {[...route_datas.conn_types, ...route_datas.apps].filter(item => item.path !== "/error-test").map(item => (
              <Route key={item.path} path={item.path} element={<ZShell title={item.title} />} />
            ))}
          </Routes>
          <RouteTransition />
        </Suspense>
      </main>
    </Router>
  );
}

// 主布局组件
export default function RootLayout() {
  const fontVariables = getFontVariables();

  return (
    <html lang="zh-CN" suppressHydrationWarning className="h-full">
      <body className={`${fontVariables} antialiased h-full`}>
        <ErrorBoundary>
          <ThemeProvider
            attribute="class"
            defaultTheme="system"
            enableSystem
            disableTransitionOnChange
          >
            <TooltipProvider>
              <ToastProvider swipeDirection="right" duration={2000}>
                <TerminalDataProvider>
                    <Toaster />
                    <WindowBtnsGroup />
                    <SidebarProvider>
                      <AppContent />
                    </SidebarProvider>
                </TerminalDataProvider>
              </ToastProvider>
            </TooltipProvider>
          </ThemeProvider>
        </ErrorBoundary>
      </body>
    </html>
  );
}

