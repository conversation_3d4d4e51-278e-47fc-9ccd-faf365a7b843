import localFont from "next/font/local";

// 思源黑体
export const sourceHanSans = localFont({
  src: "./fonts/SourceHanSansCN-Regular.otf",
  variable: "--font-source-han-sans",
  weight: "100 900",
});

// 思源宋体
export const sourceHanSerif = localFont({
  src: "./fonts/SourceHanSerifCN-Regular.otf",
  variable: "--font-source-han-serif",
  weight: "100 900",
});

// 0xProto
export const font0xProto = localFont({
  src: "./fonts/0xProto-Regular.otf",
  variable: "--font-0xproto",
  weight: "100 900",
});

// Go Mono
export const fontGoMono = localFont({
  src: "./fonts/Go-Mono.ttf",
  variable: "--font-go-mono",
  weight: "100 900",
});

// 合并所有字体变量的辅助函数
export const getFontVariables = () => {
  return `${sourceHanSans.variable} ${sourceHanSerif.variable} ${font0xProto.variable} ${fontGoMono.variable}`;
};
