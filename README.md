<h1>Site-Z 开发记录</h1>

> 查阅：
> [Rust标准库](https://doc.rust-lang.org/std/index.html)
> [Tauri v2](https://v2.tauri.app/zh-cn/start/)
> [shadcn/ui](https://ui.shadcn.com/)
> [tailwindcss](https://www.tailwindcss.cn/)
> [Lucide 图标库](https://lucide.dev/)
> [快速入门 – React 中文文档](https://zh-hans.react.dev/learn)
> [使用Tauri开发一个跨平台应用](https://app.studyraid.com/en/read/8393/231492/reading-and-writing-files)

# 创建项目

先创建一个使用shadcn/ui的[Next.js](https://nextjs.org) 项目。在要放项目文件夹的目录下，执行：
```bash
npx shadcn@latest init
```
按提示进行一些配置后，即可完成（**前端**）项目创建

接着安装Tauri的CLI工具（需要全局安装它）：
```bash
npm install -D @tauri-apps/cli@latest
```

接着在项目目录下初始化Tauri（**后端**）
```bash
npx tauri init
```

以下命令可以运行项目的开发部署服务器：
```bash
npm run dev
cargo tauri dev
npx tauri dev
```

# 安装Tauri的一些包
```bash
npm install @tauri-apps/api@latest
```

# 导入shadcn/ui的组件

## [侧边栏](https://ui.shadcn.com/docs/components/sidebar)

```bash
npx shadcn@latest add sidebar
```

![侧边栏](https://ui.shadcn.com/_next/image?url=%2Fimages%2Fsidebar-structure-dark.png&w=1920&q=75)
![SidebarMenu](https://ui.shadcn.com/_next/image?url=%2Fimages%2Fsidebar-menu-dark.png&w=1920&q=75)

# 样式应用

- 原来Home的根样式：
    ```tsx
    <div className="grid grid-rows-[20px_1fr_20px] items-center justify-items-center min-h-screen p-8 pb-20 gap-16 sm:p-20 font-[family-name:var(--font-geist-sans)]">
        <main className="flex flex-col gap-8 row-start-2 items-center sm:items-start">
        
        </main>
    </div>
    ```

- `grid-cols-[25px_1fr]` 是一个 Tailwind CSS 的类名，用于定义 CSS 网格布局中的列配置。这个类名表示网格布局有两列，其中第一列的宽度固定为 25 像素，第二列的宽度占据剩余的可用空间（即 1fr，表示 1 个比例单位的自由空间）。

# 路由

1. React Router vs Next.js 文件系统路由的主要差异：
   1. React Router：集中在代码中定义
      ```js
       <Routes>
           <Route path="/" element={<Home />} />
           <Route path="/mml" element={<ZShell title="MML" />} />
       </Routes>
      ```

   2. Next.js：通过文件系统结构定义
      ```
       app/
           ├── page.tsx          // 对应 /
           ├── mml/
           │   └── page.tsx      // 对应 /mml
           └── layout.tsx
      ```

2. 导航行为
   1. React Router：完全客户端导航，不会重新加载页面，适合单页应用(SPA)；
   2. Next.js：支持服务端导航和客户端导航，可以进行服务端渲染。本项目使用 Tauri 构建桌面应用，已在next.config.mjs配置为静态导出 (`output: 'export'`)，不需要 SSR 特性，因此更适合使用 React Router。

# Tauri中使用文件系统

运行
```bash
cargo tauri add dialog
```

