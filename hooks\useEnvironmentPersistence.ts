// hooks/useEnvironmentPersistence.ts
import { usePersistence } from './usePersistence';
import type { EnvironmentState } from '@/types/env';

interface UseEnvironmentPersistenceProps {
    environmentState: EnvironmentState;
    setEnvironmentState: React.Dispatch<React.SetStateAction<EnvironmentState>>;
}

/**
 * 验证环境状态数据格式
 */
const validateEnvironmentState = (data: any): data is EnvironmentState => {
  return (
    data &&
    typeof data === 'object' &&
    Array.isArray(data.environments) &&
    (data.activeEnvId === null || typeof data.activeEnvId === 'string') &&
    data.environments.every((env: any) => 
      env &&
      typeof env.id === 'string' &&
      typeof env.name === 'string' &&
      typeof env.addr === 'string' &&
      typeof env.port === 'number' &&
      typeof env.createdAt === 'string' &&
      typeof env.updatedAt === 'string'
    )
  );
};

/**
 * Hook: 处理环境信息的加载和保存
 *
 * 负责在应用启动时加载环境信息，并在环境信息变化时保存到文件。
 */
export function useEnvironmentPersistence({
    environmentState,
    setEnvironmentState,
}: UseEnvironmentPersistenceProps) {
    
    // 默认环境状态
    const defaultEnvironmentState: EnvironmentState = {
        environments: [],
        activeEnvId: null,
    };

    // 使用通用持久化Hook
    const { isLoaded, saveData, loadData } = usePersistence({
        data: environmentState,
        setData: setEnvironmentState,
        fileName: '.env-state.json',
        defaultData: defaultEnvironmentState,
        validateData: validateEnvironmentState,
    });

    // 返回加载状态和操作函数
    return { 
        isLoaded,
        saveEnvironmentState: saveData,
        loadEnvironmentState: loadData
    };
}
