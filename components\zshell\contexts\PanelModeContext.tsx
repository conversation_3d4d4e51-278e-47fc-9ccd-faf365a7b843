// 导入必要的React hooks
import { createContext, useContext, useState } from 'react'

// 定义面板模式的类型，包含三种状态：脚本、采数、寄存器
export type PanelMode = "脚本" | "采数" | "寄存器";

// 创建上下文对象，包含当前模式状态和设置模式的方法
// PanelModeContext 是一个持有上下文机制的对象。可以把它看作是 React Context API 创建的一个特定的“上下文实例”或“上下文句柄”。
// 它不是一个类型，但它与一个类型相关联（通过泛型参数定义了其 value 的类型）。
const PanelModeContext = createContext<{
  mode: PanelMode
  setMode: (mode: PanelMode) => void
} | null>(null)

// 提供者组件，负责管理面板模式状态
export function PanelModeProvider({ children }: { children: React.ReactNode }) {
  // 使用useState初始化面板模式，默认为"脚本"
  const [mode, setMode] = useState<PanelMode>('脚本')

  return (
    <PanelModeContext.Provider value={{ mode, setMode }}>
      {children}
    </PanelModeContext.Provider>
  )
}

// 自定义Hook，用于在组件中获取和设置面板模式
// 当使用 useContext(PanelModeContext) 时，实际上是在告诉 React：“请给我通过 PanelModeContext 这个特定的上下文通道所提供的当前值。”
export function usePanelMode() {
  const context = useContext(PanelModeContext)
  if (!context) throw new Error('usePanelMode 必须在 PanelModeProvider 组件内部使用')
  return context
}