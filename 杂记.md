```mermaid
graph TD
    A[Next.js 服务端渲染] --> B{是否有 use client}
    B -->|是| C[跳过 SSR]
    B -->|否| D[执行 SSR]
    C --> E[浏览器加载 JS]
    E --> F[执行 useEffect]
    F --> G[动态加载 Tauri API]
    G --> H[缓存到 window 对象]
```

```mermaid
sequenceDiagram
    Tauri Core->>Rust: 启动系统托盘/菜单
    Rust->>WebView: 创建浏览器实例
    WebView->>Next.js: 加载前端资源
    Next.js->>Browser: 执行客户端代码
```

```mermaid
sequenceDiagram
    participant Server as Next.js 服务器
    participant Client as 浏览器
    participant Tauri as Tauri 运行时

    Server->>Server: SSR 阶段开始
    Server->>Server: 加载 window-btn.tsx 模块
    Server->>Server: 执行顶层代码 (getCurrentWindow())
    Server-->>Tauri: ❌ 调用 Tauri API（失败）
    Server->>Client: 发送包含错误信息的 HTML

    Client->>Client: CSR 阶段开始
    Client->>Client: 执行水合（Hydration）
    Client->>Tauri: ✅ 正确调用 Tauri API
```

```mermaid
sequenceDiagram
    participant OS as 操作系统
    participant Rust as Tauri Rust Core
    participant WebView as 系统WebView
    participant Next as Next.js服务
    participant Browser as 浏览器实例

    OS->>Rust: 启动可执行文件
    Rust->>Rust: 初始化系统托盘/菜单
    Rust->>WebView: 创建WebView实例
    WebView->>Next: 请求前端资源(dev模式)
    Next->>Next: 执行SSR（开发模式特有）
    Next->>WebView: 返回渲染结果
    WebView->>Browser: 水合(Hydration)客户端组件
    Browser->>Rust: 通过Tauri API通信
```

```mermaid
graph TD
    Request[WebView请求] -->|开发模式| NextServer[Next.js开发服务器]
    NextServer --> SSR[执行SSR]
    SSR -->|含use client| ClientComponent[跳过客户端组件渲染]
    SSR -->|无use client| ServerComponent[完整服务端渲染]
    ClientComponent --> Send[发送HTML到WebView]
    Send --> Hydrate[浏览器水合]
```

```mermaid
sequenceDiagram
    participant WebView as Tauri WebView
    participant NextServer as Next.js Dev Server
    participant RustCore as Tauri Core

    WebView->>NextServer: GET /_next/...
    Note right of NextServer: 收到页面请求
    NextServer->>NextServer: 执行服务端代码
    NextServer-->>WebView: 返回SSR结果 + React水合代码
    WebView->>WebView: 执行水合
    WebView->>RustCore: 调用Tauri API
```

```mermaid
graph TD
    Build[Next.js构建] -->|生成静态文件| Dist[out目录]
    TauriBuild[Tauri打包] -->|嵌入静态文件| Binary[可执行文件]
    UserRun[用户启动] -->|加载本地文件| WebView[无需网络]
```