"use client"

import React, { useState, useEffect } from 'react';

// lucide图标
import {
    X,
    Minus,
    Squircle,
} from "lucide-react"

// shadcn/ui组件
import { Button } from '@/components/ui/button';

// 自定义 Hook 管理 tauri 窗口实例
function useAppWindow() {
    const [appWindow, setAppWindow] = useState<import('@tauri-apps/api/window').Window | null>(null);

    useEffect(() => {
        if (typeof window !== 'undefined') {
            import('@tauri-apps/api/window')
                .then(({ getCurrentWindow }) => {
                    setAppWindow(getCurrentWindow());
                })
                .catch(error => console.error('获取窗口实例失败:', error));
        }
    }, []);

    return appWindow;
}

export function WindowMinimizeBtn() {
    const appWindow = useAppWindow();
    return (
        <Button
            variant="ghost"
            size="icon"
            className="h-7 w-7"
            onClick={() => appWindow?.minimize()}
        >
            <Minus />
        </Button>
    );
}

export function WindowMaximizeBtn() {
    const appWindow = useAppWindow();
    return (
        <Button
            variant="ghost"
            size="icon"
            className="h-7 w-7"
            onClick={async () => {
                if (await appWindow?.isMaximized()) {
                    appWindow?.unmaximize();
                } else {
                    appWindow?.maximize();
                }
            }}
        >
            <Squircle />
        </Button>
    );
}

export function WindowCloseBtn() {
    const appWindow = useAppWindow();
    return (
        <Button
            variant="ghost"
            size="icon"
            className="h-7 w-7 hover:bg-red-500"
            onClick={() => appWindow?.close()}
        >
            <X />
        </Button>
    );
}

export function WindowBtnsGroup() {
    return (
        <div className='fixed top-0 right-0 flex space-x-1 m-1 z-50'>
            <WindowMinimizeBtn />
            <WindowMaximizeBtn />
            <WindowCloseBtn />
        </div>
    );
}
