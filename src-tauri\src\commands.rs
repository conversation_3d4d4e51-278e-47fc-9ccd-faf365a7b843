// src/commands.rs
// 此文件用于放置Tauri命令

use std::path::Path;

use crate::core::tcp_manager::TcpManager;
use tauri::{State, Wry};

// Tauri 命令的结果类型，错误将作为字符串发送给前端
type CommandResult<T> = std::result::Result<T, String>;

#[tauri::command]
pub async fn ping_tcp_server(
    addr: String,
    port: u16,
    manager: State<'_, TcpManager<Wry>>,
) -> CommandResult<bool> {
    manager.ping(addr, port).await.map_err(|e| e.to_string())
}

#[tauri::command]
pub async fn connect_tcp_server(
    addr: String,
    port: u16,
    manager: State<'_, TcpManager<Wry>>,
) -> CommandResult<String> {
    manager.connect(addr, port).await.map_err(|e| e.to_string())
}

#[tauri::command]
pub async fn login_tcp_server(
    addr: String,
    port: u16,
    username: String,
    password: String,
    manager: State<'_, TcpManager<Wry>>,
) -> CommandResult<String> {
    manager.login(addr, port, username, password).await.map_err(|e| e.to_string())
}

#[tauri::command]
pub async fn send_tcp_data(
    id: String,
    data: String,
    manager: State<'_, TcpManager<Wry>>,
) -> CommandResult<()> {
    manager.send_data(&id, &data).await.map_err(|e| e.to_string())
}

#[tauri::command]
pub async fn send_tcp_data_board(
    id: String,
    data: String,
    board_id: String,
    manager: State<'_, TcpManager<Wry>>,
) -> CommandResult<()> {
    manager.send_data_board(&id, &data, &board_id).await.map_err(|e| e.to_string())
}

#[tauri::command]
pub async fn disconnect_tcp_server(
    id: String,
    manager: State<'_, TcpManager<Wry>>,
) -> CommandResult<()> {
    manager.disconnect(&id).await.map_err(|e| e.to_string())
}

#[tauri::command]
pub fn exists_file(path: &str) -> bool {
    Path::new(path).exists()
}