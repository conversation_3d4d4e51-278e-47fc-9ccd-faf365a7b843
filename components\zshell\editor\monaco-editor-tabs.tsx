"use client";

import { useTheme } from "next-themes";
import { useRef } from "react";

import Editor from "@monaco-editor/react";
import type { editor } from 'monaco-editor';

// 引入 CSS 模块
import styles from './styles/editor-tabs.module.css';

import { Tabs, TabsList } from "@/components/ui/tabs";
import { TabTrigger } from "./components/tab-trigger";
import { AddTabBtn_OpenFile, AddTabBtn_NewFile } from "./components/tab-buttons";
import { DebugButton } from "./components/debug-button";
import { EmptyEditorPrompt } from "./components/empty-editor-prompt";
import { EDITOR_OPTIONS } from "./editor-constants";
import { Separator } from "@/components/ui/separator"

// 引入自定义 Hooks
import { useTabsManager } from './hooks/useTabsManager';
import { useEditorPersistence } from '@/hooks/useEditorPersistence';
import { useEditorHighlighting } from './hooks/useEditorHighlighting';
import { useTabListScroll } from './hooks/useTabListScroll';
import { useFileOperations } from './hooks/useFileOperations';
import { useEditorActions } from './hooks/useEditorActions';
import { useEditorMount } from './hooks/useEditorMount';
import { useEditorChange } from './hooks/useEditorChange';

interface MonacoEditorTabsProps {
  connectionId?: string | null;
  boardId?: string;
}

/**
 * 主编辑器组件，使用 Monaco Editor 并支持多标签页
 *
 * 通过自定义 Hooks 管理状态、持久化、高亮和 UI 交互。
 */
export function MonacoEditorTabs({ connectionId, boardId }: MonacoEditorTabsProps) {
  // --- Hooks ---

  // 主题管理
  const { resolvedTheme } = useTheme();
  const editorTheme = resolvedTheme === "dark" ? "vs-dark" : "light";

  // 标签页状态管理 Hook
  const {
    tabs,
    activeTabId,
    addTab,
    closeTab,
    updateTabContent,
    updateTabFileInfo,
    markTabAsSaved,
    setActiveTabId,
    setTabs,
  } = useTabsManager();   // 初始状态由 Hook 内部处理

  // 编辑器实例引用 (保持在主组件，传递给需要它的 Hooks)
  const editorsRef = useRef<Record<string, editor.IStandaloneCodeEditor>>({});  // Tab ID和对应的编辑器实例

  // 编辑器状态持久化 Hook
  useEditorPersistence({
    tabs,
    activeTabId,
    setTabs,
    setActiveTabId,
    addTab, // 持久化 Hook 需要 addTab 来恢复状态
  });

  // 编辑器高亮 Hook
  const {
    toggleHighlightAtCursor,
    clearAllHighlights,
    applyHighlightsToEditor, // 用于新编辑器挂载时
  } = useEditorHighlighting({ editorsRef, activeTabId });

  // 标签栏滚动 Hook
  const tabListScrollRef = useTabListScroll(40); // 应用滚动 Hook，设置敏感度

  // 文件操作 Hook
  const {
    saveActiveFile,
    handleOpenFile,
    handleOpenFileFromPrompt
  } = useFileOperations({
    addTab,
    setActiveTabId,
    updateTabFileInfo,
    markTabAsSaved
  });

  // 编辑器操作和快捷键 Hook
  const { initEditorActions } = useEditorActions({
    tabs,
    activeTabId,
    editorsRef,
    saveActiveFile,
    toggleHighlightAtCursor,
    clearAllHighlights,
    updateTabContent,
    connectionId,
    boardId
  });

  // 编辑器挂载 Hook
  const { handleEditorMount } = useEditorMount({
    editorsRef,
    activeTabId,
    setActiveTabId,
    initEditorActions,
    applyHighlightsToEditor
  });

  // 编辑器内容变化 Hook
  const { handleEditorChange } = useEditorChange({
    updateTabContent
  });

  // --- Effects ---
  // 暂无

  // --- Render ---
  return (
    <div className="h-full flex flex-col rounded-none overflow-hidden border border-border">
      {/* 标签栏 */}
      <div className="bg-background border-b border-border flex-shrink-0 relative">
        {/* 将 Tabs value 绑定到 activeTabId，确保受控 */}
        <Tabs value={activeTabId ?? ""} onValueChange={setActiveTabId}>
          {/* 将 ref 应用到 TabsList 用于滚动 */}
          <TabsList
            ref={tabListScrollRef}
            className={`rounded-none bg-background p-0 h-7 flex justify-start overflow-x-auto overflow-y-hidden whitespace-nowrap pr-[130px] ${styles.tabListScroll}`}
          >
            {tabs.map((tab) => (
              <TabTrigger
                key={tab.id}
                tab={tab} // 传递 Tab 对象
                active={activeTabId === tab.id}
                onSelect={setActiveTabId} // 直接使用 setActiveTab
                onClose={closeTab}        // 直接使用 closeTab
              />
            ))}
          </TabsList>
        </Tabs>
        {/* 添加按钮 - 固定在右侧 */}
        <div className={`${styles.fixedTabButtons} flex items-center bg-background z-10`}>
          <Separator orientation="vertical" className="h-7 w-0.5"/>
          <AddTabBtn_NewFile onClick={() => addTab(undefined, '')} />
          <AddTabBtn_OpenFile
            onClick={(filePath, content) => handleOpenFile(tabs, filePath, content)}
            tabs={tabs.map(t => ({ id: t.id, filePath: t.filePath }))} // 传递基础信息
            setActiveTab={setActiveTabId}
          />
          <DebugButton tabs={tabs} activeTabId={activeTabId} />
        </div>
      </div>

      {/* 编辑器区域 */}
      <div className="flex-1 overflow-hidden relative"> {/* 添加 relative 定位 */}
        {tabs.length > 0 ? (
          tabs.map((tab) => (
            <div
              key={tab.id}
              // 使用绝对定位和 visibility 控制显隐，避免卸载和重新挂载 Editor
              className={`${styles.editorContainer} ${tab.id === activeTabId ? styles.editorVisible : styles.editorHidden}`}
            >
              <Editor
                key={tab.id} // Key 仍然重要，用于 React 识别
                height="100%"
                language={tab.language}
                theme={editorTheme}
                value={tab.content}
                onChange={(value) => handleEditorChange(value, tab.id)} // 传递 tabId
                onMount={(editor) => handleEditorMount(editor, tab.id)} // 传递 tabId
                options={{
                  ...EDITOR_OPTIONS,
                  readOnly: false, // 确保不是只读
                }}
              />
            </div>
          ))
        ) : (
          // 没有标签页时显示提示
          <EmptyEditorPrompt onFileOpen={() => handleOpenFileFromPrompt(tabs)} />
        )}
      </div>
    </div>
  );
}
