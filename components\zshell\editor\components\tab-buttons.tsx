"use client";

import { Plus, FolderOpen } from "lucide-react";

import type { TabBasicInfo } from '../types'; // 引入共享类型
import { useToast } from "@/hooks/use-toast";
import { showOpenFileDialog, readFileContent } from '../utils/tauri-utils'; // 引入 Tauri 工具

// 基础按钮属性接口
interface BaseButtonProps {
  title: string;
  icon: React.ReactNode;
  onClick: () => void | Promise<void>;
  className?: string;
}

/**
 * 基础按钮组件 - 用于标签栏操作按钮
 */
const TabButton = ({ title, icon, onClick, className = "" }: BaseButtonProps) => (
  <button
    type="button"
    title={title}
    className={`h-7 px-2 bg-background hover:bg-muted text-muted-foreground hover:text-foreground transition-colors ${className}`}
    onClick={onClick}
  >
    {icon}
  </button>
);

// 打开文件按钮属性接口
export interface OpenFileButtonProps {
  onClick: (filePath: string, content: string) => void;
  tabs: TabBasicInfo[];
  setActiveTab: (id: string) => void;
}

/**
 * 打开文件按钮组件
 */
export const AddTabBtn_OpenFile = ({ onClick }: OpenFileButtonProps) => {
  const { toast } = useToast();

  const handleOpenFileClick = async () => {
    try {
      const selectedPath = await showOpenFileDialog(); // 使用工具函数
      if (!selectedPath) return; // 用户取消

      // 检查文件是否已经打开 (逻辑移到主组件的 handleOpenFile 中)
      // 这里直接调用 onClick 回调，传递路径和内容
      const contents = await readFileContent(selectedPath); // 使用工具函数
      onClick(selectedPath, contents); // 调用传入的回调

    } catch (error) {
      toast({
        title: "打开文件失败",
        description: `${error}`,
        variant: "destructive",
        duration: Infinity,
      });
    }
  };

  return (
    <TabButton
      title="打开文件"
      icon={<FolderOpen className="h-4 w-4" />}
      onClick={handleOpenFileClick} // 调用内部处理函数
    />
  );
};

// 新建文件按钮属性接口
export interface NewFileButtonProps {
  onClick: () => void;
}

/**
 * 新建文件按钮组件
 *
 * 创建一个新的空白标签页
 */
export const AddTabBtn_NewFile = ({ onClick }: NewFileButtonProps) => (
  <TabButton
    title="新建文件"
    icon={<Plus className="h-4 w-4" />}
    onClick={onClick}
  />
);
