"use client";

import { useTheme } from "next-themes";
import Editor from "@monaco-editor/react";

const EDITOR_OPTIONS = {
  minimap: { enabled: false },
  fontSize: 14,
  fontFamily: "var(--font-0xproto)",
  lineNumbers: "off",
  automaticLayout: true,
  formatOnPaste: true,
  formatOnType: true,
  padding: { top: 8, bottom: 8 },
  mouseWheelZoom: true,
  readOnly: false,
  scrollBeyondLastLine: false,
  scrollbar: {
    vertical: 'visible',
    horizontal: 'visible',
    useShadows: false,
    verticalScrollbarSize: 10,
    horizontalScrollbarSize: 10
  }
} as const;

export function RegisterPanel() {
  // 主题
  const { resolvedTheme } = useTheme();
  const editorTheme = resolvedTheme === "dark" ? "vs-dark" : "light";
  
  return (
    <div className="h-full flex flex-col rounded-none overflow-hidden border border-border">
      <div className="bg-background border-b border-border p-2">
        <h3 className="text-sm font-medium">寄存器查询面板</h3>
      </div>
      <div className="flex-1 overflow-hidden relative">
        <Editor
          height="100%"
          language="json"
          theme={editorTheme}
          value={`
{
  "registers": [
    {
      "address": "0x1000",
      "name": "控制寄存器",
      "value": "0xA5",
      "access": "RW"
    },
    {
      "address": "0x1004",
      "name": "状态寄存器",
      "value": "0x00",
      "access": "RO"
    }
  ]
}`}
          options={{
            ...EDITOR_OPTIONS,
          }}
        />
      </div>
    </div>
  );
}
