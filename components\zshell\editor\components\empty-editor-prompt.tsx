"use client";

import { FolderOpen } from "lucide-react";

// 组件属性接口
export interface EmptyEditorPromptProps {
  /**
   * 当用户点击提示区域时触发的回调函数
   * （文件选择和处理逻辑由父组件负责）
   */
  onFileOpen: () => void;
}

/**
 * 空编辑器提示组件
 */
export const EmptyEditorPrompt = ({ onFileOpen }: EmptyEditorPromptProps) => {
  return (
    <div
      className="h-full flex items-center justify-center text-muted-foreground cursor-pointer hover:bg-muted/50 transition-colors"
      onClick={onFileOpen} // 直接调用传入的回调
      title="点击打开文件" // 添加 title 提示
    >
      <div className="text-center">
        <FolderOpen className="mx-auto h-12 w-12 opacity-50 mb-2" />
        <p>点击此处打开一个脚本文件...</p>
      </div>
    </div>
  );
};

export default EmptyEditorPrompt;