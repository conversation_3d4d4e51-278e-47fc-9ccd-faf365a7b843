"use client";

import React, { createContext, useContext, useState, useEffect, useRef } from 'react';
import { listen } from "@tauri-apps/api/event";

// TCP数据载荷类型
interface TcpDataPayload {
    id: string;
    board_id: string;
    data: string;
    timestamp: string;
}

// 终端数据存储类型
interface TerminalDataStore {
    [key: string]: string; // key: connectionId-boardId 或 connectionId, value: 终端数据
}

// Context类型
interface TerminalDataContextType {
    getTerminalData: (connectionId: string, boardId?: string) => string;
    setTerminalData: (connectionId: string, boardId: string | undefined, data: string) => void;
    clearTerminalData: (connectionId: string, boardId?: string) => void;
    clearAllData: () => void;
}

const TerminalDataContext = createContext<TerminalDataContextType | undefined>(undefined);

export function TerminalDataProvider({ children }: { children: React.ReactNode }) {
    const [terminalDataStore, setTerminalDataStore] = useState<TerminalDataStore>({});
    const unlistenRef = useRef<(() => void) | null>(null);

    // 生成存储键
    const generateKey = (connectionId: string, boardId?: string) => {
        return boardId ? `${connectionId}-${boardId}` : connectionId;
    };

    // 获取终端数据
    const getTerminalData = (connectionId: string, boardId?: string): string => {
        const key = generateKey(connectionId, boardId);
        return terminalDataStore[key] || "";
    };

    // 设置终端数据
    const setTerminalData = (connectionId: string, boardId: string | undefined, data: string) => {
        const key = generateKey(connectionId, boardId);
        setTerminalDataStore(prev => ({
            ...prev,
            [key]: data
        }));
    };

    // 清空特定终端数据
    const clearTerminalData = (connectionId: string, boardId?: string) => {
        const key = generateKey(connectionId, boardId);
        setTerminalDataStore(prev => {
            const newStore = { ...prev };
            delete newStore[key];
            return newStore;
        });
    };

    // 清空所有数据
    const clearAllData = () => {
        setTerminalDataStore({});
    };

    // 全局监听TCP事件
    useEffect(() => {
        const setupGlobalListener = async () => {
            try {
                console.log('[TerminalDataProvider] 设置全局TCP数据监听器');
                
                // 监听TCP数据接收事件
                const unlistenData = await listen<TcpDataPayload>('tcp_data_received', (event) => {
                    const payload = event.payload;
                    console.log('[TerminalDataProvider] 收到TCP数据事件:', payload);

                    // 格式化时间戳
                    const timestamp = new Date(payload.timestamp).toLocaleTimeString();

                    // 将十六进制数据解码为文本
                    let displayData = payload.data;
                    try {
                        const bytes = new Uint8Array(payload.data.match(/.{1,2}/g)?.map(byte => parseInt(byte, 16)) || []);
                        const decoder = new TextDecoder('utf-8', { fatal: false });
                        const decodedText = decoder.decode(bytes);
                        if (decodedText && /[\x20-\x7E]/.test(decodedText)) {
                            displayData = decodedText;
                        }
                    } catch (error) {
                        console.warn('解码十六进制数据失败:', error);
                    }

                    // 添加新数据到对应的终端存储
                    const newLine = `[${timestamp}] ${payload.board_id ? `[${payload.board_id}] ` : ''}${displayData}\n`;
                    
                    // 更新对应的终端数据
                    const key = generateKey(payload.id, payload.board_id || undefined);
                    setTerminalDataStore(prev => ({
                        ...prev,
                        [key]: (prev[key] || "") + newLine
                    }));

                    console.log(`[TerminalDataProvider] 更新终端数据 key: ${key}, 新行: ${newLine.trim()}`);
                });

                // 监听TCP连接关闭事件
                const unlistenClosed = await listen<TcpDataPayload>('tcp_closed', (event) => {
                    const payload = event.payload;
                    console.log('[TerminalDataProvider] 收到TCP关闭事件:', payload);

                    const timestamp = new Date().toLocaleTimeString();
                    const newLine = `[${timestamp}] 连接已断开: ${payload.data}\n`;

                    // 更新所有相关的终端数据
                    setTerminalDataStore(prev => {
                        const newStore = { ...prev };
                        Object.keys(newStore).forEach(key => {
                            if (key.startsWith(payload.id)) {
                                newStore[key] = newStore[key] + newLine;
                            }
                        });
                        return newStore;
                    });
                });

                // 监听TCP错误事件
                const unlistenError = await listen<TcpDataPayload>('tcp_error', (event) => {
                    const payload = event.payload;
                    console.log('[TerminalDataProvider] 收到TCP错误事件:', payload);

                    const timestamp = new Date().toLocaleTimeString();
                    const newLine = `[${timestamp}] 错误: ${payload.data}\n`;

                    // 更新所有相关的终端数据
                    setTerminalDataStore(prev => {
                        const newStore = { ...prev };
                        Object.keys(newStore).forEach(key => {
                            if (key.startsWith(payload.id)) {
                                newStore[key] = newStore[key] + newLine;
                            }
                        });
                        return newStore;
                    });
                });

                // 组合清理函数
                unlistenRef.current = () => {
                    unlistenData();
                    unlistenClosed();
                    unlistenError();
                };

            } catch (error) {
                console.error('[TerminalDataProvider] 设置全局监听器失败:', error);
            }
        };

        setupGlobalListener();

        return () => {
            console.log('[TerminalDataProvider] 清理全局监听器');
            if (unlistenRef.current) {
                unlistenRef.current();
                unlistenRef.current = null;
            }
        };
    }, []);

    const contextValue: TerminalDataContextType = {
        getTerminalData,
        setTerminalData,
        clearTerminalData,
        clearAllData,
    };

    return (
        <TerminalDataContext.Provider value={contextValue}>
            {children}
        </TerminalDataContext.Provider>
    );
}

export function useTerminalData() {
    const context = useContext(TerminalDataContext);
    if (context === undefined) {
        throw new Error('useTerminalData must be used within a TerminalDataProvider');
    }
    return context;
}
