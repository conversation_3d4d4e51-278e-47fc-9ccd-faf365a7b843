// hooks/useEditorPersistence.ts
import { useCallback, useRef } from 'react';
import { useToast } from "@/hooks/use-toast";
import { usePersistence } from './usePersistence';
import type { Tab, EditorState } from '@/components/zshell/editor/types';
import { checkFileExists, readFileContent } from '@/components/utils/file-utils';
import { getLanguageIdByFileName } from '@/components/zshell/editor/utils/monaco-utils';

interface UseEditorPersistenceProps {
    tabs: Tab[];
    activeTabId: string | null;
    setTabs: React.Dispatch<React.SetStateAction<Tab[]>>;
    setActiveTabId: React.Dispatch<React.SetStateAction<string | null>>;
    addTab: (filePath: string, content: string, options?: { activate?: boolean }) => string;
}

/**
 * 验证编辑器状态数据格式
 */
const validateEditorState = (data: any): data is EditorState => {
  return (
    data &&
    typeof data === 'object' &&
    Array.isArray(data.tabs) &&
    (data.activeTabId === null || typeof data.activeTabId === 'string')
  );
};

/**
 * Hook: 处理编辑器状态的加载和保存
 *
 * 负责在应用启动时加载上次的状态，并在状态变化或应用关闭前保存当前状态。
 */
export function useEditorPersistence({
    tabs,
    activeTabId,
    setTabs,
    setActiveTabId,
    addTab,
}: UseEditorPersistenceProps) {
    const { toast } = useToast();
    const isLoadedRef = useRef(false); // 跟踪是否已完成初始加载

    // 准备持久化的数据
    const editorState: EditorState = {
        tabs: tabs
            .filter(tab => !!tab.filePath)      // 过滤掉没有filePath的tab
            .map(tab => ({
                id: tab.id,
                title: tab.title,
                filePath: tab.filePath as string, // 类型断言，因为已经过滤
            })),
        // 确保 activeTabId 对应一个实际存在的、有文件路径的标签页，否则设为 null
        activeTabId: tabs.some(t => t.id === activeTabId && !!t.filePath) ? activeTabId : null,
    };

    // 默认编辑器状态
    const defaultEditorState: EditorState = {
        tabs: [],
        activeTabId: null,
    };

    // 自定义数据设置函数，用于处理编辑器状态的特殊逻辑
    const setEditorState = useCallback(async (state: EditorState) => {
        // 如果状态文件为空或 tabs 数组为空，则加载为空状态
        if (!state || !state.tabs || state.tabs.length === 0) {
            // useTabsManager 会创建默认标签页，这里只需确保 activeTabId 正确
            setTabs(prev => prev.length === 0 ? [] : prev); // 触发一次更新确保 manager 的默认逻辑
            setActiveTabId(null); // 明确设置 activeTabId 为 null
            isLoadedRef.current = true;
            return;
        }

        // 验证文件是否存在并加载内容
        const loadedTabs: Tab[] = [];
        for (const savedTab of state.tabs) {
            if (savedTab.filePath) {
                try {
                    const fileExists = await checkFileExists(savedTab.filePath);
                    if (fileExists) {
                        const content = await readFileContent(savedTab.filePath);
                        // 使用 addTab 函数来创建 Tab 对象，但不激活
                        // 注意：这里直接构建 Tab 对象，因为 addTab 会触发状态更新，可能导致冲突
                        loadedTabs.push({
                            id: savedTab.id,
                            title: savedTab.title,
                            content: content,
                            language: getLanguageIdByFileName(savedTab.title), // 确保调用工具函数
                            filePath: savedTab.filePath,
                            isSaved: true // 从状态加载的视为已保存
                        });
                    } else {
                        console.warn(`文件不存在: ${savedTab.filePath}`);
                    }
                } catch (readError) {
                    toast({
                        title: "加载部分状态失败",
                        description: `无法读取文件 ${savedTab.filePath}: ${readError}`,
                        variant: "destructive",
                        duration: 5000,
                    });
                }
            }
        }

        // 设置标签页和活动标签
        if (loadedTabs.length > 0) {
            setTabs(loadedTabs); // 直接设置恢复的标签页

            // 确定新的 activeTabId
            const newActiveTabId = state.activeTabId && loadedTabs.some(t => t.id === state.activeTabId)
                ? state.activeTabId
                : loadedTabs[0].id; // 如果之前的 activeTab 无效，则激活第一个

            setActiveTabId(newActiveTabId);

        } else {
            // useTabsManager 会创建默认标签页
            setTabs([]); // 清空可能存在的旧默认标签
            setActiveTabId(null); // 确保 activeTabId 为 null
        }

        isLoadedRef.current = true;
    }, [setTabs, setActiveTabId, toast]);

    // 使用通用持久化Hook
    const { isLoaded, saveData, loadData } = usePersistence({
        data: editorState,
        setData: setEditorState,
        fileName: '.editor-state.json',
        defaultData: defaultEditorState,
        validateData: validateEditorState,
    });

    // 返回加载状态，如果需要的话
    return { 
        isLoaded: isLoadedRef.current || isLoaded,
        saveEditorState: saveData,
        loadEditorState: loadData
    };
}
