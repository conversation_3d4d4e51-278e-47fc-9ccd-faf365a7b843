// src/errors.rs

// 导入 thiserror crate 的 Error derive宏，用于方便地实现 std::error::Error trait。
use thiserror::Error;

// 定义一个公共枚举 AppError，用于表示应用程序中可能发生的各种错误。
// #[derive(Debug, Error)] 会自动为这个枚举实现 Debug trait (用于调试打印)
// 和 std::error::Error trait (标准的错误处理trait)。
#[derive(Debug, Error)]
pub enum AppError {
    // #[error("IO错误: {0}")] 定义了当这个错误变体被 Display (例如用 {} 打印) 时的格式。
    // {0} 会被替换为括号中字段的值。
    // #[from] 属性会自动为 std::io::Error 实现 From<std::io::Error> for AppError，
    // 使得可以使用 `?` 操作符将 std::io::Error 自动转换为 AppError::Io。
    #[error("IO错误: {0}")]
    Io(#[from] std::io::Error),

    // 类似于 Io 错误，处理 Tokio 任务的 JoinError。
    #[error("Tokio Join错误: {0}")]
    JoinError(#[from] tokio::task::JoinError),

    // 自定义错误：当找不到指定的TCP连接时使用。
    #[error("连接未找到: {0}")]
    ConnectionNotFound(String), // 包含未找到的连接ID

    // 自定义错误：当通过TCP连接发送数据失败时使用。
    #[error("发送数据失败: {0}")]
    SendDataFailed(String), // 包含失败的详细信息

    // 自定义错误：当日志系统初始化失败时使用。
    #[error("日志初始化失败: {0}")]
    LoggerInitializationError(String), // 包含初始化失败的详细信息

    // 自定义错误：当提供的IP地址或端口号格式无效时使用。
    #[error("无效的地址格式: {0}")]
    InvalidAddress(String), // 包含无效的地址字符串

    // 自定义错误：当十六进制字符串解码为字节时失败。
    // #[from] hex::FromHexError 会自动将 hex crate 中的解码错误转换为此变体。
    #[error("数据解码失败: {0}")]
    HexDecodingError(#[from] hex::FromHexError),

    // 一个通用的内部错误，用于其他未明确分类的错误。
    #[error("内部错误: {0}")]
    InternalError(String), // 包含内部错误的描述
}

// 为 AppError 实现 serde::Serialize trait。
// 这通常是为了让 Tauri 的命令 (command) 能够将这个错误类型序列化后返回给前端。
// 当一个 Tauri 命令返回 Result<T, AppError> 并且发生错误时，
// AppError 需要能被序列化成例如 JSON 字符串，以便前端JavaScript可以接收和处理。
impl serde::Serialize for AppError {
    fn serialize<S>(&self, serializer: S) -> std::result::Result<S::Ok, S::Error>
    where
        S: serde::Serializer, // S 是一个泛型序列化器
    {
        // 这里选择将整个错误（通过其 Display 实现，即 #[error(...)] 中的格式化字符串）
        // 序列化为一个简单的字符串。
        serializer.serialize_str(self.to_string().as_ref())
    }
}

// 定义一个全局的 Result 类型别名。
// pub type Result<T> = std::result::Result<T, AppError>;
// 这意味着在整个项目中，当你写 Result<MyType> 时，它实际上是 std::result::Result<MyType, AppError>。
// 这使得函数签名更简洁，并且统一了项目中错误处理的类型。
pub type Result<T> = std::result::Result<T, AppError>;